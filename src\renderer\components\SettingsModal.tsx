import React, { useState, useEffect } from 'react';
import { X, Key, Zap, Settings as SettingsIcon, AlertCircle, CheckCircle, Wrench } from 'lucide-react';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  isDarkMode: boolean;
}

const electronAPI = (window as any).electronAPI;

export const SettingsModal: React.FC<SettingsModalProps> = ({ isOpen, onClose, isDarkMode }) => {
  const [token, setToken] = useState('');
  const [tokenStatus, setTokenStatus] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);
  const [isRepairingTrellis, setIsRepairingTrellis] = useState(false);
  const [isRepairingHunyuan, setIsRepairingHunyuan] = useState(false);

  const fetchTokenStatus = async () => {
    try {
      const existingToken = await electronAPI.getConfig('huggingface-token');
      setTokenStatus(!!existingToken);
    } catch (error) {
      console.error('Error fetching token status:', error);
      setTokenStatus(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchTokenStatus();
    }
  }, [isOpen]);

  const handleSaveToken = async () => {
    const tokenToSave = token.trim();
    if (!tokenToSave) {
      setMessage({ type: 'error', text: 'Please enter a valid Hugging Face token' });
      return;
    }

    setIsLoading(true);
    setMessage({ type: 'info', text: 'Validating token... This may take a moment.' });

    try {
      // Step 1: Validate the token using the new backend endpoint
      const validationResult = await electronAPI.validateHFToken(tokenToSave);

      if (!validationResult.success) {
        throw new Error(validationResult.error || 'Token is invalid.');
      }
      
      setMessage({ type: 'info', text: 'Token is valid. Saving...' });

      // Step 2: If validation is successful, then save the token
      const saveResult = await electronAPI.setConfig('huggingface-token', tokenToSave);

      if (saveResult.success) {
        setMessage({ 
          type: 'success', 
          text: 'Token validated and saved successfully!' 
        });
        setToken(''); // Clear the input for security
        await fetchTokenStatus(); // Refresh status
      } else {
        throw new Error(saveResult.error || 'Failed to save token after validation.');
      }
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : 'An unexpected error occurred';
      setMessage({ type: 'error', text: `Error: ${errorMessage}` });
      // Clear the invalid token from storage just in case something went wrong
      await electronAPI.setConfig('huggingface-token', '');
      await fetchTokenStatus();
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearToken = async () => {
    setIsLoading(true);
    setMessage(null);

    try {
      const result = await electronAPI.setConfig('huggingface-token', '');

      if (result.success) {
        setMessage({
          type: 'info',
          text: 'Token cleared.'
        });
        setToken('');
        await fetchTokenStatus();
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to clear token' });
      }
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : 'An unexpected error occurred';
      setMessage({ type: 'error', text: `Failed to clear token: ${errorMessage}. Please try again.` });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRepairTrellis = async () => {
    setIsRepairingTrellis(true);
    setMessage({ type: 'info', text: 'Starting Trellis repair... This will open a command window.' });

    try {
      const result = await electronAPI.cleanTrellisInstall();

      if (result.success) {
        setMessage({
          type: 'success',
          text: 'Trellis repair initiated successfully! Watch the command window for progress. Look for "Trellis API Server is active and listening on 127.0.0.1:7960" - when you see this, the installation is complete and you can close the window.'
        });
      } else {
        setMessage({
          type: 'error',
          text: result.error || 'Failed to start Trellis repair'
        });
      }
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : 'An unexpected error occurred';
      setMessage({
        type: 'error',
        text: `Failed to repair Trellis: ${errorMessage}`
      });
    } finally {
      setIsRepairingTrellis(false);
    }
  };

  const handleRepairHunyuan = async () => {
    setIsRepairingHunyuan(true);
    setMessage({ type: 'info', text: 'Starting Hunyuan3D-2 repair... This will open a command window.' });

    try {
      const result = await electronAPI.cleanHunyuanInstall();

      if (result.success) {
        setMessage({
          type: 'success',
          text: 'Hunyuan3D-2 repair initiated successfully! Watch the command window for progress. Look for "Server is running on http://127.0.0.1:7861" - when you see this, the installation is complete and you can close the window.'
        });
      } else {
        setMessage({
          type: 'error',
          text: result.error || 'Failed to start Hunyuan3D-2 repair'
        });
      }
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : 'An unexpected error occurred';
      setMessage({
        type: 'error',
        text: `Failed to repair Hunyuan3D-2: ${errorMessage}`
      });
    } finally {
      setIsRepairingHunyuan(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 w-full max-w-md mx-4 shadow-xl`}>
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <SettingsIcon className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
            <h2 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Application Settings
            </h2>
          </div>
          <button
            onClick={onClose}
            className={`p-1 rounded-full ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
          >
            <X className={`w-5 h-5 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
          </button>
        </div>

        {/* Current Status */}
        <div className={`p-4 rounded-lg mb-6 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <div className="flex items-center gap-2">
            {tokenStatus ? (
              <CheckCircle className="w-4 h-4 text-green-500" />
            ) : (
              <AlertCircle className="w-4 h-4 text-yellow-500" />
            )}
            <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              {tokenStatus ? 'Hugging Face Token is Set' : 'Hugging Face Token Not Set'}
            </span>
          </div>
        </div>

        {/* Token Input */}
        <div className="space-y-4">
          <div>
            <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Hugging Face Token
            </label>
            <div className="relative">
              <Key className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
              <input
                type="password"
                value={token}
                onChange={(e) => setToken(e.target.value)}
                placeholder="hf_xxxxxxxxxxxxxxxxxxxx"
                className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-400'
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20`}
                disabled={isLoading}
              />
            </div>
            <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              Get your token from{' '}
              <a
                href="https://huggingface.co/settings/tokens"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 hover:underline"
              >
                huggingface.co/settings/tokens
              </a>
              <br />
              <strong>Important:</strong> Make sure your token has "Read" permissions and access to gated models.
            </p>
          </div>

          {/* Message */}
          {message && (
            <div className={`p-3 rounded-lg ${
              message.type === 'success' 
                ? 'bg-green-100 text-green-800 border border-green-200' 
                : message.type === 'error'
                ? 'bg-red-100 text-red-800 border border-red-200'
                : 'bg-blue-100 text-blue-800 border border-blue-200'
            }`}>
              <p className="text-sm">{message.text}</p>
            </div>
          )}

          {/* Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              onClick={handleSaveToken}
              disabled={isLoading || !token.trim()}
              className={`flex-1 px-4 py-2 rounded-lg text-white font-medium ${
                isLoading || !token.trim()
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700'
              }`}
            >
              {isLoading ? 'Saving...' : 'Save Token'}
            </button>
            
            {tokenStatus && (
              <button
                onClick={handleClearToken}
                disabled={isLoading}
                className={`px-4 py-2 rounded-lg font-medium ${
                  isLoading
                    ? 'bg-gray-400 text-white cursor-not-allowed'
                    : isDarkMode
                    ? 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Clear Token
              </button>
            )}
          </div>
        </div>

        {/* 3D Pipeline Repair Section */}
        <div className={`mt-6 p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <div className="flex items-center gap-2 mb-4">
            <Wrench className={`w-4 h-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
            <h3 className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              3D Pipeline Repair
            </h3>
          </div>

          <p className={`text-xs mb-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Use these options if you're experiencing issues with 3D generation pipelines.
            This will clean reinstall the virtual environments and dependencies.
          </p>

          <div className="flex gap-3">
            <button
              onClick={handleRepairTrellis}
              disabled={isRepairingTrellis || isRepairingHunyuan}
              className={`flex-1 px-3 py-2 rounded-lg text-sm font-medium ${
                isRepairingTrellis || isRepairingHunyuan
                  ? 'bg-gray-400 text-white cursor-not-allowed'
                  : isDarkMode
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isRepairingTrellis ? 'Repairing...' : 'Repair Trellis'}
            </button>

            <button
              onClick={handleRepairHunyuan}
              disabled={isRepairingTrellis || isRepairingHunyuan}
              className={`flex-1 px-3 py-2 rounded-lg text-sm font-medium ${
                isRepairingTrellis || isRepairingHunyuan
                  ? 'bg-gray-400 text-white cursor-not-allowed'
                  : isDarkMode
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-green-600 text-white hover:bg-green-700'
              }`}
            >
              {isRepairingHunyuan ? 'Repairing...' : 'Repair Hunyuan3D-2'}
            </button>
          </div>
        </div>

        {/* Info */}
        <div className={`mt-6 p-3 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            <strong>About FLUX Schnell:</strong> FLUX Schnell is the fastest and highest quality text-to-image model available.
            It requires a Hugging Face token with access to gated models.<br/><br/>
            <strong>Token Requirements:</strong><br/>
            • Must start with "hf_"<br/>
            • Needs "Read" permissions<br/>
            • Must have access to gated/restricted models<br/><br/>
            You can select your preferred image generation model in the text-to-3D settings panel.
          </p>
        </div>
      </div>
    </div>
  );
};
