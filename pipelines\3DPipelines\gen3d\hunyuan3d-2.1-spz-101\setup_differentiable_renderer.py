#!/usr/bin/env python3
"""
Windows-compatible setup script for DifferentiableRenderer
Based on the Windows fixes from lzz19980125/Hunyuan3D-2.1-Windows
"""

import os
import sys
import subprocess
from pathlib import Path

def setup_differentiable_renderer():
    """Set up DifferentiableRenderer with Windows compatibility"""
    
    print("Setting up DifferentiableRenderer with Windows compatibility...")
    
    # Get paths
    current_dir = Path(__file__).parent
    hunyuan_dir = current_dir / "Hunyuan3D-2.1-main"
    diff_renderer_dir = hunyuan_dir / "hy3dpaint" / "DifferentiableRenderer"
    
    if not diff_renderer_dir.exists():
        print(f"Error: DifferentiableRenderer directory not found: {diff_renderer_dir}")
        return False
    
    print(f"Working in: {diff_renderer_dir}")
    
    # Create Windows-compatible setup.py for DifferentiableRenderer
    setup_py_content = '''
import os
import sys
from pybind11.setup_helpers import Pybind11Extension, build_ext
from pybind11 import get_cmake_dir
import pybind11
from setuptools import setup, Extension

# Windows-specific compiler flags
extra_compile_args = []
extra_link_args = []

if os.name == 'nt':  # Windows
    extra_compile_args = [
        '/wd4838',  # Disable specific warnings
        '/D_ALLOW_COMPILER_AND_STL_VERSION_MISMATCH',
        '/std:c++14',  # Use C++14 standard
    ]
else:  # Linux/Mac
    extra_compile_args = ['-std=c++14']

# Define the extension
ext_modules = [
    Pybind11Extension(
        "mesh_inpaint_processor",
        ["mesh_inpaint_processor.cpp"],
        include_dirs=[
            pybind11.get_include(),
        ],
        language='c++',
        cxx_std=14,
        extra_compile_args=extra_compile_args,
        extra_link_args=extra_link_args,
    ),
]

setup(
    name="mesh_inpaint_processor",
    ext_modules=ext_modules,
    cmdclass={"build_ext": build_ext},
    zip_safe=False,
    python_requires=">=3.7",
)
'''
    
    # Write setup.py to DifferentiableRenderer directory
    setup_py_path = diff_renderer_dir / "setup.py"
    try:
        with open(setup_py_path, 'w', encoding='utf-8') as f:
            f.write(setup_py_content)
        print(f"Created setup.py: {setup_py_path}")
    except Exception as e:
        print(f"Error creating setup.py: {e}")
        return False
    
    # Check if mesh_inpaint_processor.cpp exists and add Windows headers if needed
    cpp_file = diff_renderer_dir / "mesh_inpaint_processor.cpp"
    if cpp_file.exists():
        try:
            with open(cpp_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Add Windows-compatible headers if not present
            headers_to_add = [
                '#include <array>',
                '#include <utility>',
                '#include <iostream>'
            ]
            
            modified = False
            for header in headers_to_add:
                if header not in content:
                    # Add header after existing includes
                    if '#include <pybind11/pybind11.h>' in content:
                        content = content.replace(
                            '#include <pybind11/pybind11.h>',
                            f'#include <pybind11/pybind11.h>\n{header}'
                        )
                        modified = True
            
            if modified:
                with open(cpp_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print("Added Windows-compatible headers to mesh_inpaint_processor.cpp")
                
        except Exception as e:
            print(f"Warning: Could not modify C++ file: {e}")
    
    # Try to build the extension
    try:
        os.chdir(diff_renderer_dir)
        
        # Set environment variables
        env = os.environ.copy()
        env['FORCE_CUDA'] = '1'
        env['TORCH_CUDA_ARCH_LIST'] = '7.5;8.0;8.6;8.9;9.0'
        
        print("Building DifferentiableRenderer extension...")
        result = subprocess.run([
            sys.executable, 'setup.py', 'build_ext', '--inplace'
        ], env=env, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✓ DifferentiableRenderer built successfully")
            return True
        else:
            print(f"✗ Build failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ Build timed out")
        return False
    except Exception as e:
        print(f"✗ Build error: {e}")
        return False
    finally:
        # Return to original directory
        os.chdir(current_dir)

if __name__ == "__main__":
    success = setup_differentiable_renderer()
    sys.exit(0 if success else 1)
