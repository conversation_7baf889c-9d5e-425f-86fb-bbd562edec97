const path = require('path');
const fs = require('fs').promises;
const { spawn } = require('child_process');
const { ipcMain } = require('electron');
const logger = require('./logger');
const DependencyManager = require('./dependencyManager');
const dependencyManager = new DependencyManager();
const trellisServer = require('./trellisServer');

const PYTHON_EXE = 'python'; // TODO: Unify this config
const PIPELINES_DIR = path.join(__dirname, '../../pipelines');

class PipelineLoader {
  constructor() {
    this.pipelines = {};
    this.runningProcesses = {};
  }

  async registerPipelines() {
    logger.info('Registering pipelines...');

    // Helper to recursively find all config.json files under a directory
    async function findPipelineConfigs(dir) {
      let results = [];
      const entries = await fs.readdir(dir, { withFileTypes: true });
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        if (entry.isDirectory()) {
          results = results.concat(await findPipelineConfigs(fullPath));
        } else if (entry.isFile() && entry.name === 'config.json') {
          results.push(fullPath);
        }
      }
      return results;
    }

    const configPaths = await findPipelineConfigs(PIPELINES_DIR);
    logger.info('[DEBUG] Found pipeline config.json files:', configPaths);

    for (const configPath of configPaths) {
      // Use the folder name containing config.json as the pipeline id
      const folder = path.basename(path.dirname(configPath));
      try {
        const data = await fs.readFile(configPath, 'utf-8');
        this.pipelines[folder] = JSON.parse(data);
        logger.info(`Registered pipeline: ${folder} (from ${configPath})`);
      } catch (error) {
        logger.warn(`Could not register pipeline at ${configPath}: ${error.message}`);
      }
    }

    logger.info('[DEBUG] Final registered pipelines:', Object.keys(this.pipelines));
  }

  getRegisteredPipelines() {
    // Use the merged pipelines from dependency manager, which includes both file-based and embedded configs
    const allPipelines = dependencyManager.pipelines || this.pipelines;
    const pipelines = Object.entries(allPipelines).map(([id, cfg]) => ({
      id,
      name: cfg.name || id,
      description: cfg.description || '',
      available: true, // could be enhanced later
      features: cfg.features || [],
      models: cfg.models || [],
    }));

    logger.info('[DEBUG] getRegisteredPipelines returning:', pipelines.map(p => p.id));
    return pipelines;
  }

  async runPipeline(pipelineName, data = {}, progressCb = () => {}) {
    logger.info(`runPipeline request: ${pipelineName}`);
    logger.info('runPipeline: Registered pipelines at call time:', Object.keys(this.pipelines));

    const pipeline = this.pipelines[pipelineName];
    if (!pipeline) {
      logger.error(`Unregistered pipeline: ${pipelineName}`);
      return { success: false, error: 'Pipeline not found' };
    }

    // Special case: just start the Hunyuan server without generation
    if (pipelineName === 'hunyuan2-spz-101' && data.action === 'ensure_server_running') {
      logger.info('Starting Hunyuan server for Light Enhancer...');
      try {
        const hunyaunServer = require('./hunyaunServer');
        const isRunning = await hunyaunServer.isHunyaunRunning();
        if (!isRunning) {
          logger.info('Hunyuan server not running, starting it...');
          hunyaunServer.startHunyaunServer(progressCb);
          await hunyaunServer.waitForHunyaunReady(null, progressCb);
          logger.info('Hunyuan server started successfully');
        } else {
          logger.info('Hunyuan server already running');
        }
        return { success: true, message: 'Hunyuan server is running' };
      } catch (error) {
        logger.error('Failed to start Hunyuan server:', error);
        return { success: false, error: error.message };
      }
    }

    // --- Hunyaun3d-2 and Hunyuan3D-2.1 Integration (MOVED BEFORE DEPENDENCY CHECK) ---
    if (pipelineName.toLowerCase() === 'hunyaun3d-2' || pipelineName.toLowerCase() === 'hunyuan2-spz-101' || pipelineName.toLowerCase() === 'hunyuan3d-2.1-spz-101') {
      try {
        // Handle Hunyuan3D-2.1 separately
        if (pipelineName.toLowerCase() === 'hunyuan3d-2.1-spz-101') {
          // Create progress callback
          let progressCallbackCounter = 0;
          const wrappedProgressCb = (progressData) => {
            progressCallbackCounter++;
            progressData = {
              ...progressData,
              id: progressCallbackCounter,
              timestamp: Date.now()
            };
            progressCb(progressData);
          };

          // Get image path and convert to absolute path
          const relativeImagePath = data.image_path || data.input_image_path || data.input_image || data.image;
          if (!relativeImagePath) {
            throw new Error('No image path provided for Hunyuan3D-2.1 generation.');
          }

          // Convert relative path to absolute path
          const imagePath = path.isAbsolute(relativeImagePath)
            ? relativeImagePath
            : path.join(__dirname, '../../', relativeImagePath);

          logger.info(`[Hunyuan3D-2.1] Image path resolution:`);
          logger.info(`  Relative path: ${relativeImagePath}`);
          logger.info(`  Absolute path: ${imagePath}`);

          // Use the official Hunyuan3D-2.1 server integration
          const hunyaunServer = require('./hunyaunServer');

          wrappedProgressCb({ stage: 'hunyuan3d-2.1', progress: 10, message: 'Starting Hunyuan3D-2.1 generation...' });

          // Ensure default settings are applied for official API
          const defaultSettings = {
            seed: Math.floor(Math.random() * 1000000),
            remove_background: true,
            guidance_scale: 5.0,
            num_inference_steps: 5,
            octree_resolution: 256,
            num_chunks: 8000,
            texture: true,
            ...data.settings
          };

          logger.info(`[Hunyuan3D-2.1] Using settings: ${JSON.stringify(defaultSettings)}`);

          // Use the official Hunyuan3D-2.1 server integration
          const modelPath = await hunyaunServer.generate3DModel(imagePath, wrappedProgressCb, defaultSettings);

          return {
            success: true,
            model_path: modelPath,
            project_id: data.project_id || null
          };
        }

        // Handle Hunyuan3D-2 (original) - use existing hunyaunServer
        const hunyaunServer = require('./hunyaunServer');

        // Get image path and convert to absolute path
        const relativeImagePath = data.image_path || data.input_image_path || data.input_image || data.image;
        if (!relativeImagePath) {
          throw new Error('No image path provided for Hunyuan3D-2 generation.');
        }

        // Convert relative path to absolute path
        const imagePath = path.isAbsolute(relativeImagePath)
          ? relativeImagePath
          : path.join(__dirname, '../../', relativeImagePath);

        logger.info(`[Hunyuan3D-2] Starting generation with image: ${imagePath}`);

        // Use the generate3DModel method from hunyaunServer
        // This function generates its own output path and returns the relative path
        const relativePath = await hunyaunServer.generate3DModel(imagePath, progressCb, data.settings || {});

        // Convert relative path to absolute path for the response
        const absolutePath = path.join(__dirname, '../../', relativePath);

        logger.info(`[Hunyuan3D-2] Generation completed. Model saved to: ${absolutePath}`);

        return {
          success: true,
          model_path: absolutePath,
          project_id: data.project_id || null
        };

      } catch (error) {
        logger.error(`Hunyuan generation failed:`, error);
        return {
          success: false,
          error: error.message || 'Unknown error during Hunyuan generation'
        };
      }
    }
    // --- End Hunyuan Integration ---

    // --- Start: Dependency Check ---
    let depsOk = await dependencyManager.checkDependencies(pipelineName);
    if (!depsOk) {
      logger.warn(`Dependencies missing for ${pipelineName}. Triggering auto-install…`);
      try {
        await dependencyManager.installDependencies(pipelineName, 'python');
        depsOk = await dependencyManager.checkDependencies(pipelineName);
        if (!depsOk) {
          throw new Error('Dependencies were not satisfied even after a seemingly successful installation.');
        }
        logger.info(`Dependencies installed for ${pipelineName}, continuing…`);
      } catch (error) {
        logger.error(`Dependency installation process failed for ${pipelineName}: ${error.message}`);
        return { success: false, error: 'Dependency installation failed. Please check the logs.' };
      }
    }
    // --- End: Dependency Check ---

    // --- ImageGeneration Pipeline Handling ---
    if (pipelineName === 'ImageGeneration') {
      logger.info('ImageGeneration pipeline request detected');

      if (data.action === 'enhance_lighting') {
        logger.info('Lighting enhancement action requested');

        try {
          const { spawn } = require('child_process');
          const path = require('path');
          const fs = require('fs');
          const os = require('os');

          // Create temporary files for input and output
          const tempDir = os.tmpdir();
          const tempInputPath = path.join(tempDir, `lighting_input_${Date.now()}.png`);
          const tempOutputPath = path.join(tempDir, `lighting_output_${Date.now()}.png`);

          // Write image buffer to temporary file
          fs.writeFileSync(tempInputPath, Buffer.from(data.image_buffer));

          // Get Python executable for ImageGeneration pipeline (has diffusers and SDXL models)
          const pythonExe = dependencyManager._getPythonExe('ImageGeneration');
          const scriptPath = path.join(__dirname, '../../src/main/python_helpers/enhance_lighting.py');

          // Prepare arguments
          const args = [
            scriptPath,
            tempInputPath,
            tempOutputPath,
            '--strength', data.strength?.toString() || '0.7'
          ];

          if (data.use_ai) {
            args.push('--use-ai');
          }

          logger.info(`Spawning lighting enhancement: ${pythonExe} ${args.join(' ')}`);

          return new Promise((resolve) => {
            const proc = spawn(pythonExe, args, {
              stdio: ['pipe', 'pipe', 'pipe'],
              windowsHide: true
            });

            let stdout = '';
            let stderr = '';

            proc.stdout.on('data', (data) => {
              stdout += data.toString();
            });

            proc.stderr.on('data', (data) => {
              stderr += data.toString();
              logger.error(`[Lighting Enhancement]: ${data.toString()}`);
            });

            proc.on('close', (code) => {
              try {
                if (code === 0 && fs.existsSync(tempOutputPath)) {
                  // Read the enhanced image
                  const enhancedBuffer = fs.readFileSync(tempOutputPath);

                  resolve({
                    success: true,
                    enhanced_image_buffer: new Uint8Array(enhancedBuffer),
                    message: 'Lighting enhancement completed successfully'
                  });
                } else {
                  logger.error(`Lighting enhancement failed with code ${code}: ${stderr}`);
                  resolve({ success: false, error: `Enhancement failed: ${stderr}` });
                }
              } catch (e) {
                logger.error('Error processing lighting enhancement result:', e);
                resolve({ success: false, error: 'Failed to process result' });
              } finally {
                // Clean up temporary files
                try {
                  if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
                  if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
                } catch (cleanupError) {
                  logger.warn('Failed to clean up temporary files:', cleanupError);
                }
              }
            });
          });

        } catch (error) {
          logger.error('Error in lighting enhancement:', error);
          return { success: false, error: error.message };
        }
      }

      if (data.action === 'inpaint_texture') {
        logger.info('3D texture inpainting action requested');

        try {
          const { spawn } = require('child_process');
          const path = require('path');
          const fs = require('fs');
          const os = require('os');

          // Create temporary file for mask
          const tempDir = os.tmpdir();
          const tempMaskPath = path.join(tempDir, `inpaint_mask_${Date.now()}.png`);
          const tempOutputPath = path.join(tempDir, `inpainted_model_${Date.now()}.glb`);

          // Write mask buffer to temporary file
          fs.writeFileSync(tempMaskPath, Buffer.from(data.mask_buffer));

          // Get Python executable for ImageGeneration pipeline
          const pythonExe = dependencyManager._getPythonExe('ImageGeneration');
          const scriptPath = path.join(__dirname, '../../src/main/python_helpers/texture_inpainting.py');

          // Prepare arguments
          const args = [
            scriptPath,
            data.glb_path,
            tempMaskPath,
            data.prompt,
            tempOutputPath,
            '--strength', data.strength?.toString() || '0.8'
          ];

          logger.info(`Spawning 3D texture inpainting: ${pythonExe} ${args.join(' ')}`);

          return new Promise((resolve) => {
            const proc = spawn(pythonExe, args, {
              stdio: ['pipe', 'pipe', 'pipe'],
              windowsHide: true
            });

            let stdout = '';
            let stderr = '';

            proc.stdout.on('data', (data) => {
              stdout += data.toString();
            });

            proc.stderr.on('data', (data) => {
              stderr += data.toString();
              logger.error(`[3D Inpainting]: ${data.toString()}`);
            });

            proc.on('close', (code) => {
              try {
                if (code === 0) {
                  // Parse the JSON result from stdout
                  const result = JSON.parse(stdout.trim());
                  resolve(result);
                } else {
                  logger.error(`3D texture inpainting failed with code ${code}: ${stderr}`);
                  resolve({ success: false, error: `Inpainting failed: ${stderr}` });
                }
              } catch (e) {
                logger.error('Error processing 3D inpainting result:', e);
                resolve({ success: false, error: 'Failed to process result' });
              } finally {
                // Clean up temporary files
                try {
                  if (fs.existsSync(tempMaskPath)) fs.unlinkSync(tempMaskPath);
                  // Don't delete output file - it's the result
                } catch (cleanupError) {
                  logger.warn('Failed to clean up temporary files:', cleanupError);
                }
              }
            });
          });

        } catch (error) {
          logger.error('Error in 3D texture inpainting:', error);
          return { success: false, error: error.message };
        }
      }

      // Handle other ImageGeneration actions (like generate_image) here if needed
      return { success: false, error: 'Unknown ImageGeneration action' };
    }

    // --- Trellis Integration ---
    if (pipelineName.toLowerCase() === 'trellissource' || pipelineName.toLowerCase() === 'trellis-stable-projectorz-101') {
      try {
        // Create a wrapper progress callback that adds required fields for frontend
        let progressCallbackCounter = 0;
        const wrappedProgressCb = (status) => {
          const formattedStatus = {
            event: 'progress',
            session_id: data.input_image_id || 'unknown',
            stage: status.stage || 'trellis',
            progress: status.progress || 0,
            message: status.message || status.description || 'Processing...',
            description: status.message || status.description || 'Processing...'
          };

          progressCallbackCounter++;
          // Only log every 5th progress callback to reduce spam
          if (progressCallbackCounter % 5 === 0 || status.progress >= 100) {
            console.log('[Pipeline Loader] Progress update:', `${status.stage} - ${status.progress}% - ${status.message}`);
          }

          progressCb(formattedStatus);
        };

        // Check if this is text-to-3D generation (has text_prompt but no image path)
        const hasTextPrompt = data.text_prompt && data.text_prompt.trim();
        const hasImagePath = data.image_path || data.input_image_path || data.input_image || data.image;

        // Declare imageOutputPath in broader scope for later use
        let imageOutputPath = null;

        if (hasTextPrompt && !hasImagePath) {
          logger.info('runPipeline: Text-to-3D generation detected, generating image first');

          // Step 1: Generate image from text prompt
          wrappedProgressCb({ stage: 'image_generation', progress: 0, message: 'Generating image from text prompt...' });

          const PipelineManager = require('./pipelineManager');
          const pipelineManager = new PipelineManager(dependencyManager);
          const fs = require('fs').promises;

          // Create output directory for generated image
          const outputDir = path.join(__dirname, '../../output');
          await fs.mkdir(outputDir, { recursive: true });

          const timestamp = Date.now();
          imageOutputPath = path.join(outputDir, `text_generated_${timestamp}.png`);

          try {
            // Map frontend model names to backend model names
            const modelNameMap = {
              // SDXL Turbo variants
              'sdxl_turbo': 'sdxl-turbo',
              'sdxl-turbo': 'sdxl-turbo',
              // SDXL Base variants
              'sdxl': 'stable-diffusion-xl-base-1.0',
              'sdxl_base': 'stable-diffusion-xl-base-1.0',
              'sdxl-base': 'stable-diffusion-xl-base-1.0',
              'stable-diffusion-xl-base-1.0': 'stable-diffusion-xl-base-1.0',
              // SDXL Refiner variants
              'sdxl_refiner': 'stable-diffusion-xl-refiner-1.0',
              'sdxl-refiner': 'stable-diffusion-xl-refiner-1.0',
              'stable-diffusion-xl-refiner-1.0': 'stable-diffusion-xl-refiner-1.0',
              // Stable Diffusion v1.5 variants removed from text-to-3D (incomplete model)
              // 'stable_diffusion': 'stable-diffusion-v1-5', // Removed - incomplete model
              // 'sd_v1_5': 'stable-diffusion-v1-5', // Removed - incomplete model
              // 'sd-v1-5': 'stable-diffusion-v1-5', // Removed - incomplete model
              // 'stable-diffusion-v1-5': 'stable-diffusion-v1-5', // Removed - incomplete model
              // Stable Diffusion v2.1 variants
              'sd_v2_1': 'stable-diffusion-2-1',
              'sd-v2-1': 'stable-diffusion-2-1',
              'stable-diffusion-2-1': 'stable-diffusion-2-1',
              // FLUX variants
              'flux': 'flux-dev',
              'flux_dev': 'flux-dev',
              'flux-dev': 'flux-dev',
              'flux_dev_quantized': 'flux-dev-4bit',
              'flux-dev-quantized': 'flux-dev-4bit',
              'flux_dev_4bit': 'flux-dev-4bit',
              'flux-dev-4bit': 'flux-dev-4bit'
            };

            const frontendModelName = data.selected_model || 'sdxl_turbo';
            const modelName = modelNameMap[frontendModelName] || frontendModelName;

            logger.info(`Text-to-3D: Frontend model name: ${frontendModelName}, Backend model name: ${modelName}`);

            // Enhance prompt for optimal 3D generation
            const enhancedPrompt = `${data.text_prompt}, isolated object on white background, centered, full view, clean white studio lighting, professional product photography, all parts visible, complete object, no cropping, 3D model reference`;

            const imageResult = await pipelineManager.generateImage(
              enhancedPrompt,
              imageOutputPath,
              {
                model: modelName,
                num_inference_steps: 20,
                guidance_scale: 7.5,
                width: 1024,
                height: 1024
              },
              (progress) => {
                wrappedProgressCb({
                  stage: 'image_generation',
                  progress: Math.round(progress * 25), // Use first 25% for image generation
                  message: `Generating image: ${Math.round(progress * 100)}%`
                });
              }
            );

            if (!imageResult.success) {
              throw new Error(`Image generation failed: ${imageResult.error}`);
            }

            wrappedProgressCb({ stage: 'image_generation', progress: 25, message: 'Image generated successfully, starting 3D generation...' });

            // Step 2: Use generated image for 3D generation
            data.image_path = imageOutputPath;
            data.input_image_path = imageOutputPath;

          } catch (error) {
            logger.error('Image generation failed:', error);
            throw new Error(`Text-to-3D failed during image generation: ${error.message}`);
          }
        }

        wrappedProgressCb({ stage: 'trellis', progress: 25, message: 'Launching Trellis server and generating 3D model...' });
        const imagePath = data.image_path || data.input_image_path || data.input_image || data.image;
        if (!imagePath) {
          throw new Error('No image path provided for Trellis 3D generation.');
        }
        const modelPath = await trellisServer.generate3DModel(imagePath, wrappedProgressCb);

        // Return both model path and generated image path for text-to-3D workflows
        const result = { success: true, model_path: modelPath };
        if (hasTextPrompt && !hasImagePath) {
          // For text-to-3D generation, include the generated image path for thumbnail creation
          result.generated_image_path = imageOutputPath;
        }
        return result;
      } catch (err) {
        logger.error('Trellis 3D generation failed:', err);
        return { success: false, error: err.message };
      }
    }
    // --- End Trellis Integration ---

    // --- Hunyaun3d-2 and Hunyuan3D-2.1 Integration (REMOVED - Now handled before dependency check) ---
    if (false && (pipelineName.toLowerCase() === 'hunyaun3d-2' || pipelineName.toLowerCase() === 'hunyuan2-spz-101' || pipelineName.toLowerCase() === 'hunyuan3d-2.1-spz-101')) {
      try {
        // Handle Hunyuan3D-2.1 separately
        if (pipelineName.toLowerCase() === 'hunyuan3d-2.1-spz-101') {
          // Generate output path
          const outputDir = path.join(__dirname, '../../output');
          await fs.mkdir(outputDir, { recursive: true });
          const outputPath = path.join(outputDir, `hunyaun_model_${new Date().toISOString().replace(/[:.]/g, '-')}.glb`);

          // Create progress callback
          let progressCallbackCounter = 0;
          const wrappedProgressCb = (progressData) => {
            progressCallbackCounter++;
            progressData = {
              ...progressData,
              id: progressCallbackCounter,
              timestamp: Date.now()
            };
            progressCb(progressData);
          };

          // Get image path
          const imagePath = data.image_path || data.input_image_path || data.input_image || data.image;
          if (!imagePath) {
            throw new Error('No image path provided for Hunyuan3D-2.1 generation.');
          }

          // Use the Hunyuan3D-2.1 CLI wrapper to generate the model
          const hunyuan21Dir = path.join(__dirname, '..', '..', 'pipelines', '3DPipelines', 'gen3d', 'hunyuan3d-2.1-spz-101');
          const pythonExe = dependencyManager._getPythonExe('hunyuan3d-2.1-spz-101');

          wrappedProgressCb({ stage: 'hunyuan3d-2.1', progress: 10, message: 'Starting Hunyuan3D-2.1 generation...' });

          const success = await new Promise((resolve, reject) => {
            const { spawn } = require('child_process');
            const pythonProcess = spawn(pythonExe, [
              path.join(hunyuan21Dir, 'cli_wrapper.py'),
              imagePath,
              outputPath,
              JSON.stringify(data.settings || {})
            ], {
              cwd: hunyuan21Dir,
              env: {
                ...process.env,
                PYTHONPATH: hunyuan21Dir
              }
            });

            let stdout = '';
            let stderr = '';

            pythonProcess.stdout.on('data', (data) => {
              const output = data.toString();
              stdout += output;
              logger.info(`[Hunyuan3D-2.1] ${output.trim()}`);

              // Parse progress updates
              try {
                const lines = output.split('\n');
                for (const line of lines) {
                  if (line.includes('PROGRESS:')) {
                    const progressMatch = line.match(/PROGRESS:\s*(\d+)\s*-\s*(.+)/);
                    if (progressMatch) {
                      const progress = parseInt(progressMatch[1]);
                      const message = progressMatch[2];
                      wrappedProgressCb({ stage: 'hunyuan3d-2.1', progress, message });
                    }
                  }
                }
              } catch (e) {
                // Ignore parsing errors
              }
            });

            pythonProcess.stderr.on('data', (data) => {
              const output = data.toString();
              stderr += output;
              logger.error(`[Hunyuan3D-2.1 Error] ${output.trim()}`);
            });

            pythonProcess.on('close', (code) => {
              if (code === 0) {
                resolve(true);
              } else {
                reject(new Error(`Hunyuan3D-2.1 generation failed with code ${code}: ${stderr}`));
              }
            });

            pythonProcess.on('error', (error) => {
              reject(new Error(`Failed to start Hunyuan3D-2.1 process: ${error.message}`));
            });
          });

          if (!success) {
            throw new Error('Hunyuan3D-2.1 generation failed');
          }

          return {
            success: true,
            model_path: outputPath,
            project_id: data.project_id || null
          };
        }

        // Handle Hunyuan3D-2 (original)
        const hunyaunServer = require('./hunyaunServer');

        // Create a wrapper progress callback that adds required fields for frontend
        let progressCallbackCounter = 0;

        // Define stage mapping for enhanced progress tracking
        const trellisStageMapping = {
          'preprocessing': { order: 1, weight: 0.10, name: 'Preprocessing' },
          'sparse_structure': { order: 2, weight: 0.25, name: 'Sparse Structure' },
          'slat_generation': { order: 3, weight: 0.25, name: 'SLAT Generation' },
          'mesh_creation': { order: 4, weight: 0.25, name: 'Mesh Creation' },
          'texture_generation': { order: 5, weight: 0.10, name: 'Texture Generation' },
          'glb_export': { order: 6, weight: 0.05, name: 'GLB Export' }
        };

        const hunyuanStageMapping = {
          'hunyaun': { order: 1, weight: 1.0, name: 'Hunyuan3D-2' }
        };

        const imageGenStageMapping = {
          'image_generation': { order: 1, weight: 0.3, name: 'Image Generation' }
        };

        const wrappedProgressCb = (status) => {
          const currentStage = status.stage || 'hunyaun';

          // Determine which stage mapping to use based on the current stage
          let stageMapping;
          if (currentStage === 'hunyaun') {
            stageMapping = hunyuanStageMapping;
          } else if (currentStage === 'image_generation') {
            stageMapping = imageGenStageMapping;
          } else {
            stageMapping = trellisStageMapping;
          }

          const currentStageInfo = stageMapping[currentStage] || { order: 1, weight: 1.0, name: currentStage };

          // Calculate overall progress based on stage weights
          let overallProgress = 0;

          // Add completed stages
          for (const [stageName, stageInfo] of Object.entries(stageMapping)) {
            if (stageInfo.order < currentStageInfo.order) {
              overallProgress += stageInfo.weight * 100;
            } else if (stageName === currentStage) {
              overallProgress += (stageInfo.weight * (status.progress || 0));
              break;
            }
          }

          const formattedStatus = {
            event: 'progress',
            session_id: data.input_image_id || 'unknown',
            stage: currentStage,
            progress: status.progress || 0,
            stage_progress: status.progress || 0,
            overall_progress: Math.min(Math.round(overallProgress), 100),
            step: status.step || 1,
            total: status.total || 1,
            message: status.message || status.description || 'Processing...',
            stage_name: currentStageInfo.name,
            timestamp: new Date().toISOString()
          };

          progressCallbackCounter++;
          if (progressCallbackCounter % 3 === 0 || status.progress >= 100 || status.progress === 0) {
            logger.info(`[3D Progress] ${formattedStatus.stage}: ${formattedStatus.progress}% (Overall: ${formattedStatus.overall_progress}%) - ${formattedStatus.message}`);
          }

          if (progressCb) {
            progressCb(formattedStatus);
          }
        };

        // Handle text-to-3D generation (text prompt without image)
        const hasTextPrompt = data.text_prompt && data.text_prompt.trim();
        const hasImagePath = data.image_path || data.input_image_path || data.input_image || data.image;
        let imageOutputPath = null;

        if (hasTextPrompt && !hasImagePath) {
          wrappedProgressCb({ stage: 'image_generation', progress: 0, message: 'Generating image from text prompt...' });

          try {
            // Generate image first using the image generation pipeline
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            imageOutputPath = path.join(OUTPUT_DIR, `generated_image_${timestamp}.png`);

            const imageResult = await this.runPipeline(
              'ImageGeneration',
              {
                prompt: data.text_prompt,
                output_path: imageOutputPath,
                model: data.selected_model || 'sdxl-turbo',
                settings: data.settings || {}
              },
              (imageProgress) => {
                wrappedProgressCb({
                  stage: 'image_generation',
                  progress: Math.min(imageProgress.progress || 0, 24),
                  message: imageProgress.message || 'Generating image...'
                });
              }
            );

            if (!imageResult.success) {
              throw new Error(`Image generation failed: ${imageResult.error}`);
            }

            wrappedProgressCb({ stage: 'image_generation', progress: 25, message: 'Image generated successfully, starting 3D generation...' });

            // Step 2: Use generated image for 3D generation
            data.image_path = imageOutputPath;
            data.input_image_path = imageOutputPath;

          } catch (error) {
            logger.error('Image generation failed:', error);
            throw new Error(`Text-to-3D failed during image generation: ${error.message}`);
          }
        }

        wrappedProgressCb({ stage: 'hunyaun', progress: 25, message: 'Launching Hunyaun server and generating 3D model...' });
        const imagePath = data.image_path || data.input_image_path || data.input_image || data.image;
        if (!imagePath) {
          throw new Error('No image path provided for Hunyaun 3D generation.');
        }
        const modelPath = await hunyaunServer.generate3DModel(imagePath, wrappedProgressCb, data.settings || {});

        // Return both model path and generated image path for text-to-3D workflows
        const result = { success: true, model_path: modelPath };
        if (hasTextPrompt && !hasImagePath) {
          // For text-to-3D generation, include the generated image path for thumbnail creation
          result.generated_image_path = imageOutputPath;
        }
        return result;
      } catch (err) {
        logger.error('Hunyaun 3D generation failed:', err);
        return { success: false, error: err.message };
      }
    }
    // --- End Hunyaun3d-2 Integration ---

    // --- Hunyuan3D-2.1 Integration (REMOVED - Now handled in main Hunyuan section) ---
    if (false && (pipelineName.toLowerCase() === 'hunyuan3d-2.1' || pipelineName.toLowerCase() === 'hunyuan3d-2.1-spz-101')) {
      try {
        const pipelineManager = require('./pipelineManager');

        // Create a wrapper progress callback that adds required fields for frontend
        let progressCallbackCounter = 0;
        const wrappedProgressCallback = (progress) => {
          progressCallbackCounter++;
          const progressData = {
            stage: progress.stage || 'generation',
            message: progress.message || 'Processing...',
            progress: progress.progress || 0,
            id: progressCallbackCounter,
            timestamp: Date.now()
          };
          progressCb(progressData);
        };

        // Generate output path
        const outputDir = path.join(process.cwd(), 'outputs', 'hunyuan3d-2.1');
        await fs.mkdir(outputDir, { recursive: true });
        const outputPath = path.join(outputDir, `model_${Date.now()}.glb`);

        // Generate 3D model using Hunyuan3D-2.1 API wrapper
        wrappedProgressCb({ stage: 'hunyuan3d-2.1', progress: 25, message: 'Launching Hunyuan3D-2.1 and generating 3D model...' });
        const imagePath = data.image_path || data.input_image_path || data.input_image || data.image;
        if (!imagePath) {
          throw new Error('No image path provided for Hunyuan3D-2.1 generation.');
        }

        // Use the Hunyuan3D-2.1 CLI wrapper to generate the model
        const hunyuan21Dir = path.join(__dirname, '..', '..', 'pipelines', '3DPipelines', 'gen3d', 'hunyuan3d-2.1-spz-101');
        const pythonExe = dependencyManager._getPythonExe('hunyuan3d-2.1-spz-101');

        const success = await new Promise((resolve, reject) => {
          const pythonProcess = spawn(pythonExe, [
            path.join(hunyuan21Dir, 'cli_wrapper.py'),
            imagePath,
            outputPath,
            JSON.stringify(data.settings || {})
          ], {
            cwd: hunyuan21Dir,
            env: {
              ...process.env,
              PYTHONPATH: hunyuan21Dir
            }
          });

          let stdout = '';
          let stderr = '';

          pythonProcess.stdout.on('data', (data) => {
            const output = data.toString();
            stdout += output;
            logger.info(`[Hunyuan3D-2.1] ${output.trim()}`);

            // Parse progress updates
            try {
              const lines = output.split('\n');
              for (const line of lines) {
                if (line.includes('PROGRESS:')) {
                  const progressMatch = line.match(/PROGRESS:\s*(\d+)\s*-\s*(.+)/);
                  if (progressMatch) {
                    const progress = parseInt(progressMatch[1]);
                    const message = progressMatch[2];
                    wrappedProgressCb({ stage: 'hunyuan3d-2.1', progress, message });
                  }
                }
              }
            } catch (e) {
              // Ignore parsing errors
            }
          });

          pythonProcess.stderr.on('data', (data) => {
            const output = data.toString();
            stderr += output;
            logger.error(`[Hunyuan3D-2.1 Error] ${output.trim()}`);
          });

          pythonProcess.on('close', (code) => {
            if (code === 0) {
              resolve(true);
            } else {
              reject(new Error(`Hunyuan3D-2.1 generation failed with code ${code}: ${stderr}`));
            }
          });

          pythonProcess.on('error', (error) => {
            reject(new Error(`Failed to start Hunyuan3D-2.1 process: ${error.message}`));
          });
        });

        if (!success) {
          throw new Error('Hunyuan3D-2.1 generation failed');
        }

        return {
          success: true,
          model_path: outputPath,
          project_id: data.project_id || null
        };

      } catch (error) {
        logger.error('Hunyuan3D-2.1 generation failed:', error);
        return {
          success: false,
          error: error.message || 'Unknown error during Hunyuan3D-2.1 generation'
        };
      }
    }
    // --- End Hunyuan3D-2.1 Integration ---

    const pythonExeForPipeline = dependencyManager._getPythonExe(pipelineName);

    // Helper to run python script and capture stdout
    const runScript = (scriptPath, scriptArgs = []) => {
      return new Promise((resolve, reject) => {
        logger.info(`Spawning: ${pythonExeForPipeline} ${[scriptPath, ...scriptArgs].join(' ')}`);

        const env = { ...process.env };
        const scriptsDir = path.dirname(pythonExeForPipeline);
        delete env.PYTHONHOME;
        env.VIRTUAL_ENV = path.join(PIPELINES_DIR, pipelineName, 'venv');
        env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;

        const proc = spawn(pythonExeForPipeline, [scriptPath, ...scriptArgs], {
          env,
          windowsHide: true
        });

        let stdout = '';
        let stderr = '';
        let leftover = '';

        proc.stdout.on('data', (d) => {
          const txtChunk = d.toString();
          stdout += txtChunk;

          const combined = leftover + txtChunk;
          const lines = combined.split(/\r?\n/);
          leftover = lines.pop() || '';

          for (const line of lines) {
            if (!line.trim()) continue;
            try {
              const obj = JSON.parse(line);
              // Forward any structured progress objects directly
              if (obj.event === 'progress') {
                progressCb(obj);
                continue;
              }
            } catch {/* not JSON */}
            // Fallback – send raw text
            progressCb(line);
          }
        });
        proc.stderr.on('data', (d) => {
          stderr += d.toString();
          logger.error(`[${path.basename(scriptPath)}] ${d}`);
        });
        proc.on('close', (code) => {
          if (leftover.trim()) stdout += leftover;
          if (code === 0) resolve(stdout);
          else {
            logger.error(`Script failed with code ${code}. Stderr:\n${stderr}`);
            reject(new Error(`Script exited ${code}: ${stderr.split('\n').slice(-3).join(' ')}`));
          }
        });
      });
    };

    // Stage 1: primary pipeline
    logger.info('runPipeline: pipeline config:', JSON.stringify(pipeline, null, 2));
    logger.info('runPipeline: pipeline.entry_script:', pipeline.entry_script);
    if (!pipeline.entry_script) {
      logger.error(`Pipeline ${pipelineName} is missing entry_script in config!`);
      return { success: false, error: 'Pipeline entry_script missing in config.' };
    }
    const scriptPath = path.join(PIPELINES_DIR, pipelineName, pipeline.entry_script);

    // Pass data as JSON string arg
    const resultJsonRaw = await runScript(scriptPath, [JSON.stringify(data)]);
    // Parse the last non-empty line as JSON to be robust against additional prints
    const resultLines = resultJsonRaw.trim().split(/\r?\n/).filter(l => l.trim().length > 0);
    const resultJson = resultLines[resultLines.length - 1];
    let result;
    try {
      result = JSON.parse(resultJson);
    } catch (err) {
      logger.error(`Failed to parse pipeline output as JSON. Raw output:\n${resultJsonRaw}`);
      throw new Error('Pipeline returned invalid JSON');
    }

    if (result.status !== 'success') {
      throw new Error(`Pipeline script for ${pipelineName} failed: ${result.message || 'Unknown error'}`);
    }

    let modelPath = result.model_path || null;
    const videoPath = result.video_path || null;

      // If lighting optimizer requested and this is Microsoft TRELLIS
  if ((pipelineName.toLowerCase() === 'microsoft_trellis' || pipelineName.toLowerCase() === 'trellissource' || pipelineName.toLowerCase() === 'trellis-stable-projectorz-101') && data.settings?.enable_lighting_optimizer) {
      progressCb({ stage: 'lighting', message: 'Enhancing lighting with Hunyaun3D-2...' });

      const hunPipeline = this.pipelines['Hunyaun3d-2'] || this.pipelines['hunyuan2-spz-101'];
      if (!hunPipeline) {
        logger.warn('Hunyaun3D-2 pipeline missing, skipping lighting optimization');
      } else if (modelPath) {
        // This part needs re-evaluation as it references a legacy script
        // For now, we'll log a warning and skip.
        logger.warn('Lighting enhancement step is defined but uses a legacy path. Skipping.');
        // const lightingScript = path.join(_legacyDir, 'pipelines', 'trellis_pipeline', 'lighting_enhancement.py');
        // try {
        //   await runScript(lightingScript, [modelPath]);
        // } catch (e) {
        //   logger.error('Lighting enhancement failed:', e);
        // }
      }
    }

    return { success: true, model_path: modelPath, video_path: videoPath };
  }
  
  initializeIPC() {
    ipcMain.on('run-pipeline', (event, { name, args }) => {
        this.runPipeline(name, args);
    });
  }
}

const loader = new PipelineLoader();

// Add method to handle the merge after registration
loader.mergeWithDependencyManager = function() {
  // Merge pipeline loader configs with dependency manager's embedded configs
  // Priority: embedded configs override file configs for consistency
  for (const [pipelineName, config] of Object.entries(this.pipelines)) {
    if (!dependencyManager.pipelines[pipelineName]) {
      // Only add if not already present in embedded configs
      dependencyManager.pipelines[pipelineName] = config;
    } else {
      logger.info(`Pipeline ${pipelineName} already exists in embedded configs, keeping embedded version`);
    }
  }

  dependencyManager.dependencyStatus = dependencyManager.dependencyStatus || {};
  for (const pipelineName of Object.keys(dependencyManager.pipelines)) {
    if (!dependencyManager.dependencyStatus[pipelineName]) {
      dependencyManager.dependencyStatus[pipelineName] = {
        name: pipelineName,
        python: { installed: false, details: {} },
        models: { installed: false, details: {} }
      };
    }
  }
  logger.info('PipelineLoader: Final pipelines after merge:', Object.keys(dependencyManager.pipelines));
  this.initializeIPC();
};

module.exports = loader; 