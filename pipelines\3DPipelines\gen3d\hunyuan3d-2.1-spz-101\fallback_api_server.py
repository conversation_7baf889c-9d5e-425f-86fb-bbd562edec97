#!/usr/bin/env python3
"""
Fallback API Server for Hunyuan3D-2.1
Works without C++ extensions - shape generation only
"""

import os
import sys
import uuid
import asyncio
import threading
from pathlib import Path
from typing import Dict, Any, Optional
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn

# Add paths for Hunyuan3D-2.1
current_dir = Path(__file__).parent
hunyuan_dir = current_dir / "Hunyuan3D-2.1-main"
sys.path.insert(0, str(hunyuan_dir))
sys.path.insert(0, str(hunyuan_dir / "hy3dshape"))

# Global variables
app = FastAPI(title="Hunyuan3D-2.1 Fallback API", version="1.0.0")
tasks: Dict[str, Dict[str, Any]] = {}
worker_id = str(uuid.uuid4())[:8]

# Pydantic models
class GenerationRequest(BaseModel):
    image_path: str
    seed: Optional[int] = None
    guidance_scale: float = 5.0
    num_inference_steps: int = 25
    octree_resolution: int = 256
    texture_size: int = 1024
    mesh_simplify_ratio: float = 0.15
    num_chunks: int = 30

class GenerationResponse(BaseModel):
    uid: str

class StatusResponse(BaseModel):
    status: str
    progress: Optional[float] = None
    message: Optional[str] = None
    result_path: Optional[str] = None

class HealthResponse(BaseModel):
    status: str
    worker_id: str

def generate_3d_model_fallback(task_id: str, image_path: str, **kwargs):
    """Generate 3D model using shape generation only (fallback mode)"""
    try:
        print(f"[Fallback] Starting shape generation for task {task_id}")
        tasks[task_id]["status"] = "processing"
        tasks[task_id]["progress"] = 0.1
        tasks[task_id]["message"] = "Loading shape generation model..."
        
        # Import Hunyuan3D shape generation
        from hy3dshape.pipelines import Hunyuan3DDiTFlowMatchingPipeline
        from PIL import Image
        import torch
        
        # Load image
        print(f"[Fallback] Loading image: {image_path}")
        tasks[task_id]["progress"] = 0.2
        tasks[task_id]["message"] = "Loading input image..."
        
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image not found: {image_path}")
        
        image = Image.open(image_path).convert("RGB")
        
        # Load shape generation model
        print(f"[Fallback] Loading Hunyuan3D-2.1 shape model...")
        tasks[task_id]["progress"] = 0.3
        tasks[task_id]["message"] = "Loading Hunyuan3D-2.1 shape model..."
        
        model_path = 'tencent/Hunyuan3D-2.1'
        subfolder = 'hunyuan3d-dit-v2-1'
        
        pipeline_shapegen = Hunyuan3DDiTFlowMatchingPipeline.from_pretrained(
            model_path, 
            subfolder=subfolder,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
        )
        
        if torch.cuda.is_available():
            pipeline_shapegen = pipeline_shapegen.to("cuda")
        
        # Generate 3D shape
        print(f"[Fallback] Generating 3D shape...")
        tasks[task_id]["progress"] = 0.5
        tasks[task_id]["message"] = "Generating 3D shape..."
        
        # Set generation parameters
        generator = torch.Generator()
        if kwargs.get('seed'):
            generator.manual_seed(kwargs['seed'])
        
        # Generate mesh
        mesh = pipeline_shapegen(
            image=image,
            num_inference_steps=kwargs.get('num_inference_steps', 25),
            guidance_scale=kwargs.get('guidance_scale', 5.0),
            generator=generator
        )[0]
        
        print(f"[Fallback] 3D shape generated successfully")
        tasks[task_id]["progress"] = 0.8
        tasks[task_id]["message"] = "Saving 3D model..."
        
        # Save mesh
        output_dir = current_dir / "outputs"
        output_dir.mkdir(exist_ok=True)
        
        output_path = output_dir / f"{task_id}_shape_only.glb"
        mesh.export(str(output_path))
        
        print(f"[Fallback] Model saved to: {output_path}")
        tasks[task_id]["status"] = "completed"
        tasks[task_id]["progress"] = 1.0
        tasks[task_id]["message"] = "3D model generated successfully (shape only)"
        tasks[task_id]["result_path"] = str(output_path)
        
        # Clean up GPU memory
        del pipeline_shapegen
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
    except Exception as e:
        print(f"[Fallback] Error in task {task_id}: {e}")
        tasks[task_id]["status"] = "error"
        tasks[task_id]["message"] = f"Generation failed: {str(e)}"

@app.post("/send", response_model=GenerationResponse)
async def send_generation_task(request: GenerationRequest):
    """Send a 3D generation task (fallback mode - shape only)"""
    task_id = str(uuid.uuid4())
    
    # Initialize task
    tasks[task_id] = {
        "status": "queued",
        "progress": 0.0,
        "message": "Task queued for processing",
        "result_path": None
    }
    
    print(f"[Fallback] Received generation request for task {task_id}")
    print(f"[Fallback] Image path: {request.image_path}")
    print(f"[Fallback] Settings: {request.dict()}")
    
    # Start generation in background thread
    thread = threading.Thread(
        target=generate_3d_model_fallback,
        args=(task_id, request.image_path),
        kwargs=request.dict()
    )
    thread.daemon = True
    thread.start()
    
    return JSONResponse({"uid": task_id}, status_code=200)

@app.get("/status/{uid}", response_model=StatusResponse)
async def get_status(uid: str):
    """Check the status of a generation task"""
    if uid not in tasks:
        return JSONResponse({"status": "error", "message": "Task not found"}, status_code=404)
    
    task = tasks[uid]
    response = {
        "status": task["status"],
        "progress": task["progress"],
        "message": task["message"]
    }
    
    if task.get("result_path"):
        response["result_path"] = task["result_path"]
    
    return JSONResponse(response, status_code=200)

@app.get("/status")
async def server_status():
    """Server status endpoint for client health checks"""
    return JSONResponse({
        "status": "ready", 
        "worker_id": worker_id, 
        "active_tasks": len(tasks),
        "mode": "fallback_shape_only"
    }, status_code=200)

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return JSONResponse({"status": "healthy", "worker_id": worker_id}, status_code=200)

if __name__ == "__main__":
    print("=" * 60)
    print("Hunyuan3D-2.1 Fallback API Server")
    print("=" * 60)
    print("Mode: Shape generation only (no texture)")
    print("This fallback server works without C++ extensions")
    print("Server will be available at: http://127.0.0.1:8080")
    print("=" * 60)
    
    uvicorn.run(app, host="127.0.0.1", port=8080, log_level="info")
