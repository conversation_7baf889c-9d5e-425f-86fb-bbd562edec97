#!/usr/bin/env python3
"""
Windows-compatible setup script for custom_rasterizer
Based on the Windows fixes from lzz19980125/Hunyuan3D-2.1-Windows
"""

import os
import sys
import subprocess
from pathlib import Path

def setup_custom_rasterizer():
    """Set up custom_rasterizer with Windows compatibility"""
    
    print("Setting up custom_rasterizer with Windows compatibility...")
    
    # Get paths
    current_dir = Path(__file__).parent
    hunyuan_dir = current_dir / "Hunyuan3D-2.1-main"
    rasterizer_dir = hunyuan_dir / "hy3dpaint" / "custom_rasterizer"
    
    if not rasterizer_dir.exists():
        print(f"Error: custom_rasterizer directory not found: {rasterizer_dir}")
        return False
    
    print(f"Working in: {rasterizer_dir}")
    
    # Check if setup.py exists and apply Windows fixes
    setup_py_path = rasterizer_dir / "setup.py"
    if setup_py_path.exists():
        try:
            with open(setup_py_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Apply Windows-specific fixes to setup.py
            windows_fixes = '''
# Windows-specific compiler flags
import os
if os.name == 'nt':  # Windows
    extra_compile_args = {
        'cxx': ['/wd4838', '/D_ALLOW_COMPILER_AND_STL_VERSION_MISMATCH'], 
        'nvcc': ['-allow-unsupported-compiler', '-D_ALLOW_COMPILER_AND_STL_VERSION_MISMATCH']
    }
    # Disable ninja for stability on Windows
    from torch.utils.cpp_extension import BuildExtension
    cmdclass = {"build_ext": BuildExtension.with_options(use_ninja=False)}
else:
    extra_compile_args = {'cxx': [], 'nvcc': []}
    cmdclass = {}
'''
            
            # Add Windows fixes if not already present
            if 'os.name == \'nt\'' not in content:
                # Insert Windows fixes after imports
                import_end = content.find('from torch.utils.cpp_extension import')
                if import_end != -1:
                    # Find end of import line
                    import_end = content.find('\n', import_end) + 1
                    content = content[:import_end] + windows_fixes + content[import_end:]
                    
                    # Update setup() call to use Windows-compatible settings
                    if 'cmdclass=' not in content:
                        content = content.replace('setup(', 'setup(\n    cmdclass=cmdclass,')
                    
                    with open(setup_py_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print("Applied Windows compatibility fixes to setup.py")
                    
        except Exception as e:
            print(f"Warning: Could not modify setup.py: {e}")
    
    # Check for CUDA GPU kernel fixes
    cuda_file = rasterizer_dir / "rasterizer_gpu.cu"
    if cuda_file.exists():
        try:
            with open(cuda_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Fix Windows data type casting issues
            if 'z_min.data_ptr<long>()' in content:
                content = content.replace(
                    'z_min.data_ptr<long>()',
                    'z_min.data_ptr<int64_t>()'
                )
                content = content.replace(
                    'z_max.data_ptr<long>()',
                    'z_max.data_ptr<int64_t>()'
                )
                
                with open(cuda_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print("Applied Windows CUDA kernel fixes")
                
        except Exception as e:
            print(f"Warning: Could not modify CUDA file: {e}")
    
    # Try to build the extension
    try:
        os.chdir(rasterizer_dir)
        
        # Set environment variables for Windows compilation
        env = os.environ.copy()
        env['FORCE_CUDA'] = '1'
        env['TORCH_CUDA_ARCH_LIST'] = '7.5;8.0;8.6;8.9;9.0'
        
        print("Building custom_rasterizer extension...")
        
        # Try pip install approach first (recommended)
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-e', '.', 
            '--no-build-isolation', '--verbose'
        ], env=env, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✓ custom_rasterizer built successfully")
            return True
        else:
            print(f"✗ Build failed: {result.stderr}")
            
            # Try fallback approach
            print("Trying fallback build approach...")
            result2 = subprocess.run([
                sys.executable, '-m', 'pip', 'install', 'torch', 'torchvision', 'pybind11', 'ninja'
            ], env=env, capture_output=True, text=True, timeout=300)
            
            if result2.returncode == 0:
                result3 = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', '-e', '.', '--no-deps', '--verbose'
                ], env=env, capture_output=True, text=True, timeout=600)
                
                if result3.returncode == 0:
                    print("✓ custom_rasterizer built successfully (fallback)")
                    return True
                else:
                    print(f"✗ Fallback build also failed: {result3.stderr}")
                    return False
            else:
                print("✗ Could not install build dependencies")
                return False
            
    except subprocess.TimeoutExpired:
        print("✗ Build timed out")
        return False
    except Exception as e:
        print(f"✗ Build error: {e}")
        return False
    finally:
        # Return to original directory
        os.chdir(current_dir)

if __name__ == "__main__":
    success = setup_custom_rasterizer()
    sys.exit(0 if success else 1)
