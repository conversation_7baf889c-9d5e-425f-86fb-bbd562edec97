#!/usr/bin/env python3
"""
Simplified Hunyuan3D-2.1 API Server
This server provides the same API as the official server but avoids C++ extension issues.
"""

import os
import sys
import uuid
import json
import base64
import argparse
import subprocess
import threading
from pathlib import Path
from typing import Dict, Any, Optional

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# API Models
class GenerationRequest(BaseModel):
    image: str = Field(..., description="Base64 encoded input image")
    remove_background: bool = Field(True, description="Remove background")
    texture: bool = Field(True, description="Generate textures")
    seed: int = Field(1234, description="Random seed")
    octree_resolution: int = Field(256, description="Octree resolution")
    num_inference_steps: int = Field(5, description="Inference steps")
    guidance_scale: float = Field(5.0, description="Guidance scale")
    num_chunks: int = Field(8000, description="Number of chunks")

class GenerationResponse(BaseModel):
    uid: str = Field(..., description="Task ID")

class StatusResponse(BaseModel):
    status: str = Field(..., description="Task status")
    model_base64: Optional[str] = Field(None, description="Base64 encoded model")
    message: Optional[str] = Field(None, description="Status message")

class HealthResponse(BaseModel):
    status: str = Field(..., description="Health status")
    worker_id: str = Field(..., description="Worker ID")

# Global variables
app = FastAPI(
    title="Simplified Hunyuan3D-2.1 API",
    description="Simplified API server for Hunyuan3D-2.1 without C++ extensions",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Task storage
tasks: Dict[str, Dict[str, Any]] = {}
worker_id = str(uuid.uuid4())[:6]

def run_generation_task(task_id: str, request_data: Dict[str, Any]):
    """Run generation task in background"""
    try:
        # Update task status
        tasks[task_id]["status"] = "processing"
        
        # Save image to temporary file
        image_data = base64.b64decode(request_data["image"])
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)
        
        input_path = temp_dir / f"{task_id}_input.png"
        output_path = temp_dir / f"{task_id}_output.glb"
        
        with open(input_path, "wb") as f:
            f.write(image_data)
        
        # Prepare settings
        settings = {
            "quality": "high",
            "seed": request_data.get("seed", 1234),
            "remove_background": request_data.get("remove_background", True),
            "guidance_scale": request_data.get("guidance_scale", 5.0),
            "num_inference_steps": request_data.get("num_inference_steps", 5),
            "octree_resolution": request_data.get("octree_resolution", 256),
            "num_chunks": request_data.get("num_chunks", 8000),
            "enable_texture": request_data.get("texture", True)
        }
        
        # Update status to texturing
        tasks[task_id]["status"] = "texturing"
        
        # Create a temporary Python script that uses the official demo pattern
        temp_script = Path(__file__).parent / "temp_inference.py"

        # Write the inference script
        script_content = f'''
import sys
import os
from pathlib import Path

print("Starting Hunyuan3D-2.1 inference...")

# Add paths
hunyuan_dir = Path(__file__).parent / "Hunyuan3D-2.1-main"
sys.path.insert(0, str(hunyuan_dir))
sys.path.insert(0, str(hunyuan_dir / "hy3dshape"))
sys.path.insert(0, str(hunyuan_dir / "hy3dpaint"))

try:
    print("Loading dependencies...")
    from PIL import Image
    import torch
    print("Basic dependencies loaded")

    # Try to import Hunyuan modules with fallbacks
    try:
        from hy3dshape.rembg import BackgroundRemover
        print("Background remover loaded")
    except ImportError as e:
        print(f"Warning: Could not load background remover: {{e}}")
        BackgroundRemover = None

    try:
        from hy3dshape.pipelines import Hunyuan3DDiTFlowMatchingPipeline
        print("Shape generation pipeline loaded")
    except ImportError as e:
        print(f"Error: Could not load shape generation pipeline: {{e}}")
        raise

    # Load image
    print("Loading input image...")
    image = Image.open("{str(input_path)}")
    if image.mode != 'RGB':
        image = image.convert('RGB')
    print(f"Image loaded: {{image.size}}")

    # Remove background if needed and available
    if {settings.get("remove_background", True)} and BackgroundRemover is not None:
        print("Removing background...")
        try:
            rembg = BackgroundRemover()
            image = rembg(image)
            print("Background removed")
        except Exception as e:
            print(f"Warning: Background removal failed: {{e}}")

    # Generate shape
    print("Loading shape generation model...")
    model_path = 'tencent/Hunyuan3D-2.1'
    subfolder = 'hunyuan3d-dit-v2-1'
    pipeline_shapegen = Hunyuan3DDiTFlowMatchingPipeline.from_pretrained(model_path, subfolder=subfolder)
    print("Model loaded, generating 3D shape...")

    mesh = pipeline_shapegen(image=image)[0]
    print("3D shape generated")

    # Save the mesh directly (skip texture for now to test basic functionality)
    print("Saving 3D model...")
    mesh.export("{str(output_path)}")
    print("3D model saved successfully")

    print("SUCCESS: Generation completed")

except Exception as e:
    print(f"ERROR: {{e}}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
'''

        with open(temp_script, 'w') as f:
            f.write(script_content)

        # Run the temporary script
        cmd = [sys.executable, str(temp_script)]
        
        print(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path(__file__).parent / "Hunyuan3D-2.1-main")

        print(f"Official API stdout: {result.stdout}")
        if result.stderr:
            print(f"Official API stderr: {result.stderr}")

        # Clean up temporary script
        try:
            temp_script.unlink()
        except:
            pass

        if result.returncode == 0:
            # Success - read the generated model
            if output_path.exists():
                with open(output_path, "rb") as f:
                    model_data = f.read()
                
                model_base64 = base64.b64encode(model_data).decode()
                tasks[task_id]["status"] = "completed"
                tasks[task_id]["model_base64"] = model_base64
                
                # Cleanup temp files
                try:
                    input_path.unlink()
                    output_path.unlink()
                except:
                    pass
            else:
                tasks[task_id]["status"] = "error"
                tasks[task_id]["message"] = "Output file not generated"
        else:
            # Error
            tasks[task_id]["status"] = "error"
            tasks[task_id]["message"] = f"Generation failed: {result.stderr}"
            print(f"Generation failed for task {task_id}: {result.stderr}")
    
    except Exception as e:
        tasks[task_id]["status"] = "error"
        tasks[task_id]["message"] = str(e)
        print(f"Exception in generation task {task_id}: {e}")

@app.post("/send", response_model=GenerationResponse)
async def send_generation_task(request: GenerationRequest):
    """Send a 3D generation task to be processed asynchronously"""
    task_id = str(uuid.uuid4())
    
    # Initialize task
    tasks[task_id] = {
        "status": "processing",
        "model_base64": None,
        "message": None
    }
    
    # Start generation in background
    thread = threading.Thread(
        target=run_generation_task,
        args=(task_id, request.dict())
    )
    thread.daemon = True
    thread.start()
    
    return JSONResponse({"uid": task_id}, status_code=200)

@app.get("/status/{uid}", response_model=StatusResponse)
async def get_status(uid: str):
    """Check the status of a generation task"""
    if uid not in tasks:
        return JSONResponse({"status": "error", "message": "Task not found"}, status_code=404)
    
    task = tasks[uid]
    response = {
        "status": task["status"],
        "message": task.get("message")
    }
    
    if task["status"] == "completed" and task.get("model_base64"):
        response["model_base64"] = task["model_base64"]
    
    return JSONResponse(response, status_code=200)

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return JSONResponse({"status": "healthy", "worker_id": worker_id}, status_code=200)

@app.get("/status")
async def server_status():
    """Server status endpoint for client health checks"""
    return JSONResponse({"status": "ready", "worker_id": worker_id, "active_tasks": len(tasks)}, status_code=200)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--host", type=str, default="127.0.0.1")
    parser.add_argument("--port", type=int, default=8080)
    args = parser.parse_args()
    
    print(f"Starting Simplified Hunyuan3D-2.1 API Server on {args.host}:{args.port}")
    print("This version avoids C++ extensions for better compatibility")
    
    uvicorn.run(app, host=args.host, port=args.port, log_level="info")
