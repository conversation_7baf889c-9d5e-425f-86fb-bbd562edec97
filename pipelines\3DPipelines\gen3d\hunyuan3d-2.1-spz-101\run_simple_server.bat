@echo off
REM Simplified Hunyuan3D-2.1 Server (No C++ Extensions)
REM This script runs Hunyuan3D-2.1 without the problematic C++ extensions

echo Simplified Hunyuan3D-2.1 Server (No C++ Extensions)
echo ================================================

REM Set the current directory to the script location
cd /d "%~dp0"

REM Use system Python with development headers (required for C++ extensions)
set PYTHON_EXE=python
where python >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found in PATH
    echo Please install Python with development headers from python.org
    pause
    exit /b 1
)

REM Check if virtual environment exists, create if not
if not exist "env\Scripts\activate.bat" (
    echo Creating virtual environment with system Python...
    %PYTHON_EXE% -m venv env
    if errorlevel 1 (
        echo Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
call env\Scripts\activate.bat
if errorlevel 1 (
    echo Failed to activate virtual environment
    pause
    exit /b 1
)

REM Install build tools and core dependencies for C++ extensions
echo Installing build tools and core dependencies...
pip install --upgrade pip setuptools wheel

REM Install C++ build dependencies
echo Installing C++ build dependencies...
pip install ninja pybind11

REM Set up Visual Studio build environment
call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if errorlevel 1 (
    echo Warning: Visual Studio Build Tools not found at expected location
    echo Attempting to continue with system compiler...
)

REM Install PyTorch with CUDA support
echo Installing PyTorch with CUDA support...
pip install torch==2.5.1 torchvision==0.20.1 torchaudio==2.5.1 --index-url https://download.pytorch.org/whl/cu124

REM Install core ML dependencies
echo Installing core ML dependencies...
pip install transformers==4.46.0
pip install diffusers==0.30.0
pip install accelerate==1.1.1
pip install numpy==1.24.4
pip install opencv-python==*********
pip install trimesh==4.4.7
pip install pygltflib==1.16.3
pip install omegaconf==2.3.0
pip install tqdm==4.66.5
pip install einops==0.8.0
pip install fastapi==0.115.12
pip install uvicorn==0.34.3
pip install imageio==2.36.0
pip install scikit-image==0.24.0
pip install rembg==2.0.65
pip install onnxruntime-gpu

REM Install additional dependencies
pip install huggingface-hub safetensors

REM Ensure we have the official repository
if not exist "Hunyuan3D-2.1-main" (
    echo Error: Official Hunyuan3D-2.1 repository not found
    echo The repository should already be present from dependency installation
    pause
    exit /b 1
)

REM Change to the official repository directory
cd Hunyuan3D-2.1-main

REM Install the package in development mode
echo Installing Hunyuan3D-2.1 in development mode...
pip install -e .

REM Build custom rasterizer
if exist "hy3dpaint\custom_rasterizer" (
    echo Building custom rasterizer...
    cd hy3dpaint\custom_rasterizer
    pip install -e .
    if errorlevel 1 (
        echo Error: Failed to build custom rasterizer
        pause
        exit /b 1
    )
    cd ..\..
)

REM Build mesh painter
if exist "hy3dpaint\DifferentiableRenderer" (
    echo Building mesh painter...
    cd hy3dpaint\DifferentiableRenderer
    python setup_mesh_inpaint.py build_ext --inplace
    if errorlevel 1 (
        echo Error: Failed to build mesh painter
        pause
        exit /b 1
    )
    cd ..\..
)

REM Download Real-ESRGAN model if needed
if not exist "hy3dpaint\ckpt\RealESRGAN_x4plus.pth" (
    echo Downloading Real-ESRGAN model...
    mkdir hy3dpaint\ckpt 2>nul
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth' -OutFile 'hy3dpaint\ckpt\RealESRGAN_x4plus.pth'"
)

REM Set environment variables for 12GB VRAM optimization
set CUDA_VISIBLE_DEVICES=0
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

REM Return to main directory
cd ..

REM Check if installation is already complete
if exist "hunyuan21_init_done.txt" (
    echo Installation already complete, starting server...
    goto :start_server
)

echo Installation not complete, running installation first...
call install_official_hunyuan.bat
if errorlevel 1 (
    echo Installation failed
    pause
    exit /b 1
)

:start_server
REM Run the simple API server that uses the official repository
echo.
echo Starting Hunyuan3D-2.1 API Server on port 8080...
echo Server will be available at: http://127.0.0.1:8080
echo.
echo Press Ctrl+C to stop the server
echo ================================================

python simple_api_server.py --host 127.0.0.1 --port 8080

pause
