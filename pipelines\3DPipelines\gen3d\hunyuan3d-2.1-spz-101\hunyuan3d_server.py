#!/usr/bin/env python3
"""
Official Hunyuan3D-2.1 Server Implementation
Following the exact process from the official repository with 12GB VRAM optimizations.
"""

import os
import sys
import time
import uuid
import json
import torch
import trimesh
import numpy as np
from pathlib import Path
from typing import Optional, Dict, Any, Union
from PIL import Image

# Add Hunyuan3D-2.1 paths to Python path
current_dir = Path(__file__).parent
hunyuan_repo_path = current_dir / "Hunyuan3D-2.1"
sys.path.insert(0, str(hunyuan_repo_path / "hy3dshape"))
sys.path.insert(0, str(hunyuan_repo_path / "hy3dpaint"))

# Import official Hunyuan3D-2.1 components
try:
    from hy3dshape import Hunyuan3DDiTFlowMatchingPipeline
    from hy3dshape.pipelines import export_to_trimesh
    from hy3dshape.rembg import BackgroundRemover
    from hy3dpaint.textureGenPipeline import Hunyuan3DPaintPipeline, Hunyuan3DPaintConfig
    from hy3dpaint.convert_utils import create_glb_with_pbr_materials
except ImportError as e:
    print(f"Error importing Hunyuan3D-2.1 components: {e}")
    print("Please ensure Hunyuan3D-2.1 repository is properly installed.")
    sys.exit(1)

class Hunyuan3DServer:
    """Official Hunyuan3D-2.1 Server with 12GB VRAM optimization"""
    
    def __init__(self, low_vram_mode: bool = True):
        self.low_vram_mode = low_vram_mode
        self.shape_pipeline = None
        self.texture_pipeline = None
        self.background_remover = None
        self.output_dir = current_dir / "output"
        self.output_dir.mkdir(exist_ok=True)
        
        # Initialize components
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize Hunyuan3D-2.1 components with VRAM optimization"""
        print("Initializing Hunyuan3D-2.1 components...")
        
        # Initialize background remover
        print("Loading background remover...")
        self.background_remover = BackgroundRemover()
        
        # Initialize shape generation pipeline
        print("Loading shape generation pipeline...")
        self.shape_pipeline = Hunyuan3DDiTFlowMatchingPipeline.from_pretrained(
            'tencent/Hunyuan3D-2.1',
            subfolder='hunyuan3d-dit-v2-1',
            use_safetensors=False,
            device='cuda' if torch.cuda.is_available() else 'cpu'
        )
        
        # Initialize texture generation pipeline with low VRAM config
        print("Loading texture generation pipeline...")
        paint_config = Hunyuan3DPaintConfig(
            max_num_view=6,  # Reduced for 12GB VRAM
            resolution=512   # Balanced resolution
        )
        paint_config.realesrgan_ckpt_path = str(hunyuan_repo_path / "hy3dpaint/ckpt/RealESRGAN_x4plus.pth")
        paint_config.multiview_cfg_path = str(hunyuan_repo_path / "hy3dpaint/cfgs/hunyuan-paint-pbr.yaml")
        paint_config.custom_pipeline = str(hunyuan_repo_path / "hy3dpaint/hunyuanpaintpbr")
        
        self.texture_pipeline = Hunyuan3DPaintPipeline(paint_config)
        
        # Enable CPU offloading for low VRAM mode
        if self.low_vram_mode:
            print("Enabling low VRAM mode with CPU offloading...")
            # Note: CPU offloading will be handled during generation
        
        print("Hunyuan3D-2.1 server initialized successfully!")
    
    def generate_shape(self, image_path: str, settings: Dict[str, Any]) -> Dict[str, Any]:
        """Generate 3D shape from image following official process"""
        try:
            # Load and preprocess image
            image = Image.open(image_path).convert('RGB')
            
            # Background removal (official step)
            print("Removing background...")
            image = self.background_remover(image)
            
            # Generate shape using official settings
            print("Generating 3D shape...")
            generator = torch.Generator()
            generator.manual_seed(settings.get('seed', 1234))
            
            # Use official parameter names and values
            outputs = self.shape_pipeline(
                image=image,
                num_inference_steps=settings.get('steps', 30),
                guidance_scale=settings.get('guidance_scale', 7.5),
                generator=generator,
                octree_resolution=settings.get('octree_resolution', 256),
                num_chunks=settings.get('num_chunks', 200000),
                output_type='mesh'
            )
            
            # Export to trimesh (official method)
            mesh = export_to_trimesh(outputs)[0]
            
            # Save mesh
            output_filename = f"shape_{uuid.uuid4().hex}.obj"
            output_path = self.output_dir / output_filename
            mesh.export(str(output_path))
            
            # Clear VRAM if in low VRAM mode
            if self.low_vram_mode:
                torch.cuda.empty_cache()
            
            return {
                "success": True,
                "mesh_path": str(output_path),
                "faces": len(mesh.faces),
                "vertices": len(mesh.vertices)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def generate_texture(self, mesh_path: str, image_path: str, settings: Dict[str, Any]) -> Dict[str, Any]:
        """Generate PBR texture for mesh following official process"""
        try:
            print("Generating PBR texture...")
            
            # Generate textured mesh using official texture pipeline
            output_filename = f"textured_{uuid.uuid4().hex}.obj"
            output_path = self.output_dir / output_filename
            
            # Use official texture generation
            textured_mesh_path = self.texture_pipeline(
                mesh_path=mesh_path,
                image_path=image_path,
                output_mesh_path=str(output_path),
                save_glb=False
            )
            
            # Convert to GLB with PBR materials (official method)
            glb_path = str(output_path).replace('.obj', '.glb')
            textures = {
                'albedo': str(output_path).replace('.obj', '.jpg'),
                'metallic': str(output_path).replace('.obj', '_metallic.jpg'),
                'roughness': str(output_path).replace('.obj', '_roughness.jpg')
            }
            
            create_glb_with_pbr_materials(textured_mesh_path, textures, glb_path)
            
            # Clear VRAM if in low VRAM mode
            if self.low_vram_mode:
                torch.cuda.empty_cache()
            
            return {
                "success": True,
                "textured_mesh_path": textured_mesh_path,
                "glb_path": glb_path
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def generate_all(self, image_path: str, settings: Dict[str, Any]) -> Dict[str, Any]:
        """Generate complete 3D model with texture (sequential for 12GB VRAM)"""
        try:
            # Step 1: Generate shape
            print("Step 1/2: Generating shape...")
            shape_result = self.generate_shape(image_path, settings)
            if not shape_result["success"]:
                return shape_result
            
            # Step 2: Generate texture
            print("Step 2/2: Generating texture...")
            texture_result = self.generate_texture(
                shape_result["mesh_path"], 
                image_path, 
                settings
            )
            if not texture_result["success"]:
                return texture_result
            
            return {
                "success": True,
                "shape_mesh_path": shape_result["mesh_path"],
                "textured_mesh_path": texture_result["textured_mesh_path"],
                "glb_path": texture_result["glb_path"],
                "faces": shape_result["faces"],
                "vertices": shape_result["vertices"]
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

# Global server instance
server = None

def initialize_server():
    """Initialize the global server instance"""
    global server
    if server is None:
        server = Hunyuan3DServer(low_vram_mode=True)
    return server

def generate_3d_model(image_path: str, settings: Dict[str, Any] = None) -> Dict[str, Any]:
    """Main entry point for 3D model generation"""
    if settings is None:
        settings = {
            "steps": 30,
            "guidance_scale": 7.5,
            "octree_resolution": 256,
            "num_chunks": 200000,
            "seed": int(time.time())
        }
    
    server = initialize_server()
    return server.generate_all(image_path, settings)

if __name__ == "__main__":
    # Test the server
    print("Testing Hunyuan3D-2.1 server...")
    server = initialize_server()
    print("Server ready!")
