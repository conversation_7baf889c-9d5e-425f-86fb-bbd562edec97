#!/usr/bin/env python3
"""
Hunyuan3D-2.1 CLI Wrapper
This script provides a command-line interface for Hunyuan3D-2.1 generation.
"""

import sys
import os
import json
import argparse
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description='Hunyuan3D-2.1 CLI Wrapper')
    parser.add_argument('image_path', help='Path to input image')
    parser.add_argument('output_path', help='Path for output GLB file')
    parser.add_argument('settings', help='JSON string with generation settings')
    
    args = parser.parse_args()
    
    try:
        # Parse settings
        settings = json.loads(args.settings) if args.settings else {}
        
        print("PROGRESS: 10 - Initializing Hunyuan3D-2.1...")
        
        # Validate input image
        if not os.path.exists(args.image_path):
            raise FileNotFoundError(f"Input image not found: {args.image_path}")
        
        print("PROGRESS: 20 - Loading models...")
        
        # Import Hunyuan3D-2.1 modules
        try:
            # Add the official repository paths to Python path
            current_dir = Path(__file__).parent
            hunyuan_dir = current_dir / "Hunyuan3D-2.1-main"
            sys.path.insert(0, str(hunyuan_dir))
            sys.path.insert(0, str(hunyuan_dir / "hy3dshape"))
            sys.path.insert(0, str(hunyuan_dir / "hy3dpaint"))

            # Apply torchvision fix if available
            try:
                from torchvision_fix import apply_fix
                apply_fix()
            except ImportError:
                print("Warning: torchvision_fix module not found, proceeding without compatibility fix")
            except Exception as e:
                print(f"Warning: Failed to apply torchvision fix: {e}")

            # Import required modules
            from PIL import Image
            from hy3dshape.rembg import BackgroundRemover
            from hy3dshape.pipelines import Hunyuan3DDiTFlowMatchingPipeline
            from textureGenPipeline import Hunyuan3DPaintPipeline, Hunyuan3DPaintConfig

            print("PROGRESS: 30 - Models loaded successfully")

        except ImportError as e:
            print(f"ERROR: Failed to import Hunyuan3D-2.1 modules: {e}")
            print("This may be due to missing C++ extensions or dependencies")
            sys.exit(1)
        
        print("PROGRESS: 40 - Processing input image...")
        
        # Extract settings with defaults based on HuggingFace demo settings
        quality = settings.get('quality', 'high')

        # Quality presets based on HuggingFace demo (optimized settings)
        if quality == 'low':
            # Fast generation - reduced quality
            guidance_scale = float(settings.get('guidance_scale', 3.0))
            num_inference_steps = int(settings.get('num_inference_steps', 15))
            octree_resolution = int(settings.get('octree_resolution', 128))
            num_chunks = int(settings.get('num_chunks', 4000))
        elif quality == 'medium':
            # Balanced quality and speed
            guidance_scale = float(settings.get('guidance_scale', 4.0))
            num_inference_steps = int(settings.get('num_inference_steps', 20))
            octree_resolution = int(settings.get('octree_resolution', 192))
            num_chunks = int(settings.get('num_chunks', 6000))
        elif quality == 'high':
            # HuggingFace demo settings - proven to work well
            guidance_scale = float(settings.get('guidance_scale', 5.0))
            num_inference_steps = int(settings.get('num_inference_steps', 30))
            octree_resolution = int(settings.get('octree_resolution', 256))
            num_chunks = int(settings.get('num_chunks', 8000))
        elif quality == 'ultra':
            # Maximum quality - slower generation
            guidance_scale = float(settings.get('guidance_scale', 6.0))
            num_inference_steps = int(settings.get('num_inference_steps', 40))
            octree_resolution = int(settings.get('octree_resolution', 320))
            num_chunks = int(settings.get('num_chunks', 10000))
        else:
            # Default to high quality settings
            guidance_scale = float(settings.get('guidance_scale', 5.0))
            num_inference_steps = int(settings.get('num_inference_steps', 30))
            octree_resolution = int(settings.get('octree_resolution', 256))
            num_chunks = int(settings.get('num_chunks', 8000))

        # Other settings
        seed = int(settings.get('seed', 8998107))  # Use HuggingFace demo seed as default
        remove_background = bool(settings.get('remove_background', True))  # Enable by default
        
        print(f"PROGRESS: 50 - Generating 3D model with settings:")
        print(f"  Quality Preset: {quality}")
        print(f"  Guidance Scale: {guidance_scale}")
        print(f"  Inference Steps: {num_inference_steps}")
        print(f"  Octree Resolution: {octree_resolution}")
        print(f"  Num Chunks: {num_chunks}")
        print(f"  Seed: {seed}")
        print(f"  Remove Background: {remove_background}")

        # Import Hunyuan3D-2.1 modules
        print("PROGRESS: 55 - Loading Hunyuan3D-2.1 pipelines...")
        try:
            # Add the required paths to sys.path (using the cloned source)
            hunyuan_source = current_dir / 'Hunyuan3D-2.1-main'
            sys.path.insert(0, str(hunyuan_source / 'hy3dshape'))
            sys.path.insert(0, str(hunyuan_source / 'hy3dpaint'))
            sys.path.insert(0, str(hunyuan_source))

            # Inject stub modules for Blender (bpy) if not available so that optional GLB conversion can be skipped
            import types
            if 'bpy' not in sys.modules:
                stub_bpy = types.ModuleType('bpy')
                stub_bpy.ops = types.SimpleNamespace()
                stub_bpy.ops.wm = types.SimpleNamespace(obj_import=lambda *a, **kw: None)
                stub_bpy.ops.export_scene = types.SimpleNamespace(gltf=lambda *a, **kw: None)
                stub_bpy.ops.object = types.SimpleNamespace(select_all=lambda *a, **kw: None,
                                                            shade_smooth=lambda *a, **kw: None,
                                                            shade_flat=lambda *a, **kw: None)
                stub_bpy.data = types.SimpleNamespace(objects=[], scenes={})
                stub_bpy.context = types.SimpleNamespace(scene=types.SimpleNamespace(objects=[]), window=types.SimpleNamespace(scene=None), view_layer=None)
                stub_bpy.app = types.SimpleNamespace(version=(0, 0, 0))
                sys.modules['bpy'] = stub_bpy
            # Stub Real-ESRGAN if unavailable, identity upscaler
            if 'realesrgan' not in sys.modules:
                import types, torch
                class _IdentityUpscaler:
                    def __call__(self, img):
                        return img
                stub_realesrgan = types.ModuleType('realesrgan')
                stub_realesrgan.RRDBNet = object
                def _real_esrganer(*args, **kwargs):
                    return _IdentityUpscaler()
                stub_realesrgan.RealESRGANer = _real_esrganer
                sys.modules['realesrgan'] = stub_realesrgan
                # Stub basicsr (required by image super-resolution utilities) if unavailable
                if 'basicsr' not in sys.modules:
                    import types as _types_bsr
                    _basicsr = _types_bsr.ModuleType('basicsr')
                    _archs = _types_bsr.ModuleType('basicsr.archs')
                    _rrdb_arch = _types_bsr.ModuleType('basicsr.archs.rrdbnet_arch')
                    class _RRDBNet:
                        def __init__(self, *args, **kwargs):
                            pass
                    _rrdb_arch.RRDBNet = _RRDBNet
                    _archs.rrdbnet_arch = _rrdb_arch
                    _basicsr.archs = _archs
                    sys.modules['basicsr'] = _basicsr
                    sys.modules['basicsr.archs'] = _archs
                    sys.modules['basicsr.archs.rrdbnet_arch'] = _rrdb_arch
                # Stub facexlib to satisfy optional face enhancement imports
                if 'onnxruntime' not in sys.modules:
                    # provide dummy onnxruntime to avoid rembg import crash
                    sys.modules['onnxruntime'] = types.ModuleType('onnxruntime')
                # Stub rembg (background remover) to bypass NumPy ABI issues
                if 'rembg' not in sys.modules:
                    import types as _types_rmbg
                    _rembg = _types_rmbg.ModuleType('rembg')
                    def _noop_remove(img, *args, **kwargs):
                        return img
                    def _noop_new_session(*args, **kwargs):
                        return None
                    _rembg.remove = _noop_remove
                    _rembg.new_session = _noop_new_session
                    sys.modules['rembg'] = _rembg
                # Stub facexlib to satisfy optional face enhancement imports
                if 'facexlib' not in sys.modules:
                    sys.modules['facexlib'] = types.ModuleType('facexlib')
                # Try to import mesh_inpaint_processor; if not compiled, attempt on-the-fly build
                def _ensure_extension(module_name:str, setup_rel_path:str):
                    try:
                        import importlib
                        importlib.import_module(module_name)
                        return True
                    except ImportError:
                        print(f"Extension '{module_name}' not found. Attempting to build it now...")
                        import subprocess, sys, os
                        setup_path = hunyuan_source / setup_rel_path
                        if not setup_path.exists():
                            print(f"Setup script {setup_path} not found – cannot build {module_name}.")
                            return False
                        build_cmd = [sys.executable, str(setup_path), "build_ext", "--inplace"]
                        env = os.environ.copy()
                        # Quieter build on Windows: ensure cl.exe reachable
                        try:
                            subprocess.check_call(build_cmd, cwd=str(setup_path.parent), env=env)
                            import importlib
                            importlib.invalidate_caches()
                            importlib.import_module(module_name)
                            print(f"Successfully built extension '{module_name}'.")
                            return True
                        except subprocess.CalledProcessError as ce:
                            print(f"Failed to build {module_name}: {ce}")
                            return False
                # Ensure inpaint processor
                _ensure_extension("mesh_inpaint_processor", "hy3dpaint/DifferentiableRenderer/setup_mesh_inpaint.py")
                # Ensure custom rasterizer
                _ensure_extension("custom_rasterizer.custom_rasterizer_kernel._C", "hy3dpaint/custom_rasterizer/setup.py")
                # Provide fallback stubs if still unavailable after attempted build
                try:
                    import importlib
                    importlib.import_module('mesh_inpaint_processor')
                except ImportError:
                    stub_inpaint = types.ModuleType('mesh_inpaint_processor')
                    def _meshVerticeInpaint(texture, mask, *args, **kwargs):
                        # Simple fallback: return inputs unchanged
                        return texture, mask
                    stub_inpaint.meshVerticeInpaint = _meshVerticeInpaint
                    sys.modules['mesh_inpaint_processor'] = stub_inpaint
                    sys.modules['facexlib'] = types.ModuleType('facexlib')
            if 'mathutils' not in sys.modules:
                sys.modules['mathutils'] = types.ModuleType('mathutils')

            # Import required modules
            from PIL import Image
            # Lazy import BackgroundRemover later to avoid environment issues
            BackgroundRemover = None
            try:
                from hy3dshape.rembg import BackgroundRemover as _BR
                BackgroundRemover = _BR
            except Exception as rembg_err:
                print(f"Warning: BackgroundRemover not available ({rembg_err}); proceeding without background removal")
            from hy3dshape.pipelines import Hunyuan3DDiTFlowMatchingPipeline
            from textureGenPipeline import Hunyuan3DPaintPipeline, Hunyuan3DPaintConfig

            # Apply torchvision fix if available
            try:
                from torchvision_fix import apply_fix
                apply_fix()
            except ImportError:
                print("Warning: torchvision_fix module not found, proceeding without compatibility fix")
            except Exception as e:
                print(f"Warning: Failed to apply torchvision fix: {e}")

            print("PROGRESS: 60 - Generating shape...")

            # Load and process the input image
            image = Image.open(args.image_path).convert("RGBA")

            # Apply background removal if requested
            if remove_background and BackgroundRemover is not None and image.mode == 'RGB':
                print("PROGRESS: 62 - Removing background...")
                rembg = BackgroundRemover()
                image = rembg(image)

            # Generate mesh using Hunyuan3D-Shape with HuggingFace demo settings
            shape_pipeline = Hunyuan3DDiTFlowMatchingPipeline.from_pretrained('tencent/Hunyuan3D-2.1')

            # Generate shape with specified parameters
            mesh_untextured = shape_pipeline(
                image=image,
                steps=num_inference_steps,
                guidance_scale=guidance_scale,
                seed=seed,
                octree_resolution=octree_resolution
            )[0]

            print("PROGRESS: 80 - Generating textures...")

            # Generate textures using Hunyuan3D-Paint with optimized settings
            paint_config = Hunyuan3DPaintConfig(
                max_num_view=6,
                resolution=512  # Fixed resolution for stability
            )
            paint_pipeline = Hunyuan3DPaintPipeline(paint_config)

            # Ensure output directory exists
            output_dir = Path(args.output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # Save the untextured mesh to a temporary OBJ file that the paint pipeline expects
            tmp_obj_path = output_dir / "untextured_mesh.obj"
            mesh_untextured.export(tmp_obj_path)

            print("PROGRESS: 82 - Running paint pipeline...")
            textured_obj_path = output_dir / "textured_mesh.obj"
            paint_pipeline(
                mesh_path=str(tmp_obj_path),
                image_path=args.image_path,
                output_mesh_path=str(textured_obj_path),
                use_remesh=True,
                save_glb=True
            )

            print("PROGRESS: 90 - Exporting GLB...")

            # The paint pipeline creates a GLB alongside the OBJ; use it as final output
            generated_glb = textured_obj_path.with_suffix('.glb')
            if generated_glb.exists():
                import shutil
                shutil.copy(generated_glb, args.output_path)
            else:
                # Fallback: convert OBJ to GLB if for some reason GLB wasn't generated
                mesh_textured = trimesh.load(textured_obj_path)
                mesh_textured.export(args.output_path)

        except ImportError as e:
            print(f"WARNING: Hunyuan3D-2.1 modules not available: {e}")
            print("PROGRESS: 60 - Creating placeholder model...")

            # Create output directory if it doesn't exist
            output_dir = Path(args.output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # Create a placeholder GLB file
            placeholder_content = b"GLB placeholder - Hunyuan3D-2.1 modules not installed"
            with open(args.output_path, 'wb') as f:
                f.write(placeholder_content)

        except Exception as e:
            print(f"ERROR: Hunyuan3D-2.1 generation failed: {e}")
            raise
        
        print("PROGRESS: 100 - Generation complete!")
        print(f"Model saved to: {args.output_path}")
        
    except Exception as e:
        print(f"ERROR: Hunyuan3D-2.1 generation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
