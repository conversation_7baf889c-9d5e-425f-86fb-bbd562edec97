const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const axios = require('axios');
const FormData = require('form-data');
const chalk = require('chalk');
const logger = require('./logger');
chalk.level = 3; // Force 16m color support for red logs

// Configuration constants - Updated for Official Hunyuan3D-2.1
const HUNYAUN_PORT = 8080; // Port configured in start_server.bat
const HUNYAUN_HOST = '127.0.0.1';
const RUN_BAT = path.join(__dirname, '../../pipelines/3DPipelines/gen3d/hunyuan3d-2.1-spz-101/start_server.bat');
const OUTPUT_DIR = path.join(__dirname, '../../output/hunyuan3d-2.1');

// Connection attempt counter to reduce log spam
let connectionAttemptCounter = 0;
let recoveryAttemptCounter = 0;
const MAX_RECOVERY_ATTEMPTS = 0; // Disabled auto-recovery completely
const CONNECTION_ATTEMPTS_BEFORE_RECOVERY = 999999; // Effectively disabled

// Global progress callback for server output parsing
let globalProgressCallback = null;

// Stage tracking for progress reporting
let currentStage = 'preprocessing';
let stageCompleted = {
  preprocessing: false,
  structure_generation: false,
  glb_generation: false,
  completion: false
};
let progressLineCounter = 0;

// Automated Python process cleanup function
async function cleanupPythonProcesses() {
  logHunyaunServer('Checking for lingering Python processes...');
  
  return new Promise((resolve) => {
    const cleanup = spawn('taskkill', ['/F', '/IM', 'python.exe'], { 
      stdio: 'pipe',
      shell: true 
    });
    
    cleanup.on('close', (code) => {
      if (code === 0) {
        logHunyaunServer('Python processes cleaned up successfully');
      } else {
        logHunyaunServer('No Python processes found to clean up (or cleanup failed)');
      }
      resolve();
    });
    
    cleanup.on('error', (error) => {
      logHunyaunServer('Error during Python cleanup:', error.message);
      resolve(); // Continue even if cleanup fails
    });
  });
}

const logHunyaunServer = (...args) => {
  const prefix = chalk.red('[HunyaunServer]');
  // Only use console.log to avoid logger character separation issue
  console.log(prefix, ...args);
};

// Check if Hunyaun server is running and responding to HTTP requests
async function isHunyaunRunning() {
  // Test the actual HTTP endpoint instead of just checking if port is open
  // This prevents issues where the Python process is running but HTTP server is unresponsive

  try {
    const response = await axios.get(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/health`, {
      timeout: 3000 // 3 second timeout for health check
    });

    // Only log successful connection if we had previous failures
    if (connectionAttemptCounter > 0) {
      logHunyaunServer('Server is running - HTTP endpoint responding');
    }
    connectionAttemptCounter = 0; // Reset counter on successful connection
    return true;

  } catch (error) {
    connectionAttemptCounter++;

    // Don't log routine connection errors - they're expected when server is starting
    // Only log significant events like auto-recovery

    // Auto-recovery: If we've failed many times, try to restart the server (Hunyuan takes longer to start)
    if (connectionAttemptCounter >= CONNECTION_ATTEMPTS_BEFORE_RECOVERY && recoveryAttemptCounter < MAX_RECOVERY_ATTEMPTS) {
      logHunyaunServer('🔄 Auto-recovery triggered after ' + CONNECTION_ATTEMPTS_BEFORE_RECOVERY + ' failed attempts (attempt ' + (recoveryAttemptCounter + 1) + '/' + MAX_RECOVERY_ATTEMPTS + ')');
      triggerServerRestart();
    } else if (connectionAttemptCounter >= CONNECTION_ATTEMPTS_BEFORE_RECOVERY && recoveryAttemptCounter >= MAX_RECOVERY_ATTEMPTS) {
      logHunyaunServer('❌ Max recovery attempts (' + MAX_RECOVERY_ATTEMPTS + ') reached. Server may be permanently stuck.');
    }

    return false;
  }
}

function startHunyaunServer(progressCb = null) {
  logHunyaunServer('Starting server...');
  logHunyaunServer('RUN_BAT:', RUN_BAT);
  logHunyaunServer('Batch file exists:', fs.existsSync(RUN_BAT));

  if (!fs.existsSync(RUN_BAT)) {
    throw new Error('Hunyaun run batch file not found at: ' + RUN_BAT);
  }

  // Kill any existing process first
  if (global.hunyaunProcess) {
    logHunyaunServer('Killing existing Hunyaun process...');
    global.hunyaunProcess.kill('SIGTERM');
    global.hunyaunProcess = null;
  }

  // Run the batch file using cmd.exe for consistent execution
  const process = spawn('cmd.exe', ['/c', RUN_BAT], {
    cwd: path.dirname(RUN_BAT),
    stdio: 'pipe',
    windowsHide: true
  });

  global.hunyaunProcess = process;

  process.stdout.on('data', (data) => {
    const output = data.toString();
    parseProgressFromOutput(output, progressCb);
    
    // Reduce log verbosity - only show every 10th line or important messages
    const lines = output.split('\n').filter(line => line.trim());
    lines.forEach((line, index) => {
      if (line.includes('ERROR') || line.includes('WARNING') ||
          line.includes('Server is active') || line.includes('listening on') ||
          line.includes('Loading pipeline') || line.includes('Initializing') ||
          index % 10 === 0) {
        logHunyaunServer('[STDOUT]', line);
      }
    });
  });

  process.stderr.on('data', (data) => {
    const output = data.toString();
    parseProgressFromOutput(output, progressCb);
    
    // Filter out repetitive ECONNREFUSED messages
    if (!output.includes('ECONNREFUSED')) {
      logHunyaunServer('[STDERR]', output);
    }
  });

  process.on('close', (code) => {
    logHunyaunServer('Hunyaun server process exited with code:', code);
    global.hunyaunProcess = null;
  });

  process.on('error', (error) => {
    logHunyaunServer('Error starting Hunyaun server:', error);
    global.hunyaunProcess = null;
  });

  logHunyaunServer('Hunyaun server process started');
}

// Wait for Hunyaun server to be ready
async function waitForHunyaunReady(timeout = null, progressCb = null) {
  logHunyaunServer('Waiting for Hunyaun server to be ready...');

  let waitCounter = 0;

  while (true) {
    if (await isHunyaunRunning()) return true;

    waitCounter++;

    // Only send progress callback every 5th check (every 10 seconds) to reduce spam
    if (progressCb && waitCounter % 5 === 0) {
      progressCb({ stage: 'hunyaun', message: 'Waiting for Hunyaun server to start...' });
    }

    await new Promise(r => setTimeout(r, 2000));
    // No timeout: wait forever, no auto-recovery
  }
}

// Auto-recovery function to restart the server after failed attempts
async function triggerServerRestart() {
  try {
    recoveryAttemptCounter++;
    logHunyaunServer('🔧 Starting auto-recovery process (attempt ' + recoveryAttemptCounter + '/' + MAX_RECOVERY_ATTEMPTS + ')...');

    // Step 1: Kill existing Python processes
    logHunyaunServer('🧹 Cleaning up Python processes...');
    await cleanupPythonProcesses();

    // Step 2: Kill existing Hunyaun process if it exists
    if (global.hunyaunProcess) {
      logHunyaunServer('🛑 Terminating existing Hunyaun process...');
      global.hunyaunProcess.kill('SIGTERM');
      global.hunyaunProcess = null;
    }

    // Step 3: Wait a moment for cleanup
    logHunyaunServer('⏳ Waiting for cleanup to complete...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Step 4: Reset connection counter and restart server
    connectionAttemptCounter = 0;
    logHunyaunServer('🚀 Restarting Hunyaun server...');
    startHunyaunServer();

    logHunyaunServer('✅ Auto-recovery process completed (attempt ' + recoveryAttemptCounter + '/' + MAX_RECOVERY_ATTEMPTS + ')');

  } catch (error) {
    logHunyaunServer('❌ Auto-recovery failed (attempt ' + recoveryAttemptCounter + '/' + MAX_RECOVERY_ATTEMPTS + '):', error);
  }
}

// Reset stage tracking for new generation
function resetStageTracking() {
  currentStage = 'preprocessing';
  stageCompleted = {
    preprocessing: false,
    structure_generation: false,
    glb_generation: false,
    completion: false
  };
  progressLineCounter = 0; // Reset progress line counter
  recoveryAttemptCounter = 0; // Reset recovery counter on successful new generation
  logHunyaunServer('[Hunyaun Progress] Stage tracking reset for new generation');
}

function parseProgressFromOutput(output, progressCb) {
  if (!progressCb) {
    return;
  }

  const lines = output.split('\n');

  for (const line of lines) {
    progressLineCounter++;

    // Only process every 10th line to reduce spam
    if (progressLineCounter % 10 !== 0) {
      continue;
    }

    const trimmedLine = line.trim();
    if (!trimmedLine) continue;

    // Map Hunyaun output to progress stages
    const { stage, description } = mapHunyaunMessageToStage(trimmedLine, 0);

    // Determine progress based on stage
    let progress = 0;
    if (stageCompleted.preprocessing) progress = 25;
    if (stageCompleted.structure_generation) progress = 50;
    if (stageCompleted.glb_generation) progress = 75;
    if (stageCompleted.completion) progress = 100;

    progressCb({ stage, progress, message: description });
  }
}

function mapHunyaunMessageToStage(message, progress) {
  const lowerMessage = message.toLowerCase();

  // Stage 1: Preprocessing
  if (lowerMessage.includes('loading') || lowerMessage.includes('initializing') ||
      lowerMessage.includes('preprocessing') || lowerMessage.includes('image processing')) {
    if (!stageCompleted.preprocessing) {
      currentStage = 'preprocessing';
      if (lowerMessage.includes('complete') || lowerMessage.includes('done')) {
        stageCompleted.preprocessing = true;
      }
    }
    return { stage: 'hunyaun_preprocessing', description: 'Image Preprocessing: Preparing image for 3D generation' };
  }

  // Stage 2: 3D Structure Generation
  if (lowerMessage.includes('generating 3d') || lowerMessage.includes('structure') ||
      lowerMessage.includes('inference') || lowerMessage.includes('diffusion')) {
    if (!stageCompleted.structure_generation) {
      currentStage = 'structure_generation';
      if (lowerMessage.includes('complete') || lowerMessage.includes('done') ||
          lowerMessage.includes('generated')) {
        stageCompleted.structure_generation = true;
      }
    }
    return { stage: 'hunyaun_generation', description: 'Hunyaun3D Generation: Creating 3D structure from image' };
  }

  // Stage 3: GLB Generation
  if (lowerMessage.includes('glb') || lowerMessage.includes('mesh') ||
      lowerMessage.includes('export') || lowerMessage.includes('texture')) {
    if (!stageCompleted.glb_generation) {
      currentStage = 'glb_generation';
      if (lowerMessage.includes('complete') || lowerMessage.includes('done') ||
          lowerMessage.includes('exported')) {
        stageCompleted.glb_generation = true;
      }
    }
    return { stage: 'hunyaun_export', description: 'GLB Export: Finalizing 3D model file' };
  }

  // Stage 4: Completion
  if (lowerMessage.includes('complete') || lowerMessage.includes('finished') ||
      lowerMessage.includes('success')) {
    stageCompleted.completion = true;
    return { stage: 'hunyaun_complete', description: 'Generation Complete: 3D model ready' };
  }

  // Default stage based on current stage
  const stageMap = {
    'preprocessing': { stage: 'hunyaun_preprocessing', description: 'Image Preprocessing: Preparing image for 3D generation' },
    'structure_generation': { stage: 'hunyaun_generation', description: 'Hunyaun3D Generation: Creating 3D structure from image' },
    'glb_generation': { stage: 'hunyaun_export', description: 'GLB Export: Finalizing 3D model file' },
    'completion': { stage: 'hunyaun_complete', description: 'Generation Complete: 3D model ready' }
  };

  return stageMap[currentStage] || { stage: 'hunyaun', description: 'Processing...' };
}

// Retry generation request after server restart
async function retryGenerationRequest(imagePath, progressCb, settings = {}) {
  logHunyaunServer('Executing retry generation request...');

  if (progressCb) {
    const { stage, description } = mapHunyaunMessageToStage('Retrying 3D generation...', 0);
    progressCb({ stage, progress: 0, message: description });
  }

  // Read and encode the image
  const imageBuffer = fs.readFileSync(path.resolve(imagePath));
  const base64Image = imageBuffer.toString('base64');
  const imageDataUrl = `data:image/png;base64,${base64Image}`;

  // Map settings to API format
  const apiSettings = mapSettingsToAPI(settings);
  logHunyaunServer('Mapped API settings:', apiSettings);

  // Prepare request data
  const requestData = {
    single_multi_img_input: [imageDataUrl],
    seed: Math.floor(Math.random() * 1000000),
    ...apiSettings,
    output_format: 'glb'
  };

  logHunyaunServer('Final request data:', {
    ...requestData,
    single_multi_img_input: ['[IMAGE_DATA_OMITTED]'] // Don't log the actual image data
  });

  // Send generation request to Hunyaun server (no timeout - unlimited generation time)
  logHunyaunServer('Sending retry generation request to Hunyaun server (no timeout)...');
  const requestConfig = {
    headers: {
      'Content-Type': 'application/json'
    }
    // No timeout - let it run as long as needed
  };

  let taskId = null;
  try {
    const response = await axios.post(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/send`, requestData, requestConfig);
    logHunyaunServer('Retry generation request sent successfully');

    if (response.data && response.data.uid) {
      taskId = response.data.uid;
      logHunyaunServer(`Retry generation started with task ID: ${taskId}`);
    } else {
      throw new Error('Invalid response from retry generation endpoint - no task ID received');
    }
  } catch (postError) {
    logHunyaunServer('Retry POST request failed:', postError.code, postError.message);
    throw postError;
  }

  if (taskId) {
    // Poll for status updates using official API
    let statusComplete = false;
    let pollAttempts = 0;
    const maxPollAttempts = 5; // Give it a few attempts to start responding

    while (!statusComplete) {
      try {
        const statusResp = await axios.get(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/status/${taskId}`);
        const statusData = statusResp.data;
        pollAttempts = 0; // Reset poll attempts on successful response

        if (progressCb && statusData) {
          let progress = 0;
          let message = 'Processing retry...';

          // Map official API status to progress
          if (statusData.status === 'processing') {
            progress = 25;
            message = 'Generating 3D shape (retry)...';
          } else if (statusData.status === 'texturing') {
            progress = 75;
            message = 'Generating PBR textures (retry)...';
          } else if (statusData.status === 'completed') {
            progress = 100;
            message = 'Retry generation complete!';
            statusComplete = true;

            // Save the model data from the status response
            if (statusData.model_base64) {
              const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
              const outputPath = path.join(OUTPUT_DIR, `hunyuan3d_retry_model_${timestamp}.glb`);

              // Ensure output directory exists
              if (!fs.existsSync(OUTPUT_DIR)) {
                fs.mkdirSync(OUTPUT_DIR, { recursive: true });
              }

              // Decode and save the model
              const modelBuffer = Buffer.from(statusData.model_base64, 'base64');
              fs.writeFileSync(outputPath, modelBuffer);

              logHunyaunServer('Retry model saved to:', outputPath);
              const app = require('electron').app;
              const relativePath = path.relative(app.getAppPath(), outputPath);

              // Return the path immediately since we have the model
              return relativePath;
            }
          } else if (statusData.status === 'error') {
            throw new Error(statusData.message || 'Retry generation failed');
          }

          progressCb({
            stage: 'hunyaun',
            progress: progress,
            step: progress,
            total: 100,
            message: message
          });
        }
      } catch (err) {
        pollAttempts++;
        logHunyaunServer(`Error polling /status/${taskId} endpoint during retry (attempt ${pollAttempts}/${maxPollAttempts}):`, err.message);

        // If we can't poll status after several attempts, the server might be truly down
        if (pollAttempts >= maxPollAttempts) {
          throw new Error('Server not responding to status requests after retry POST hang up');
        }
      }

      if (!statusComplete) {
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds before next poll
      }
    }
  }

  // If we reach here without returning, something went wrong
  throw new Error('Retry generation completed but no model data received');
}

// Main 3D generation function
async function generate3DModel(imagePath, progressCb, settings = {}) {
  logHunyaunServer('generate3DModel called with imagePath:', imagePath);
  logHunyaunServer('Settings received:', settings);

  // First, cleanup any lingering Python processes to prevent permission errors
  await cleanupPythonProcesses();

  const isRunning = await isHunyaunRunning();
  logHunyaunServer('isHunyaunRunning() returned:', isRunning);

  // Reset stage tracking for new generation
  resetStageTracking();

  // Set the global progress callback for server output parsing
  globalProgressCallback = progressCb;

  if (!isRunning) {
    logHunyaunServer('Server not running, starting server...');
    if (progressCb) {
      const { stage, description } = mapHunyaunMessageToStage('Starting Hunyaun server...', 0);
      progressCb({ stage, progress: 0, message: description });
    }
    try {
      startHunyaunServer(progressCb);
      logHunyaunServer('startHunyaunServer() called successfully');
    } catch (error) {
      logHunyaunServer('Error starting server:', error);
      throw error;
    }
    await waitForHunyaunReady(null, progressCb);
  } else {
    logHunyaunServer('Server already running, skipping startup');
  }

  if (progressCb) {
    const { stage, description } = mapHunyaunMessageToStage('Sending image to Hunyaun for 3D generation...', 0);
    progressCb({ stage, progress: 0, message: description });
  }

  // Read and encode the image
  const imageBuffer = fs.readFileSync(imagePath);
  const imageBase64 = imageBuffer.toString('base64');
  const imageDataUrl = `data:image/png;base64,${imageBase64}`;

  // Map UI settings to official Hunyuan3D-2.1 API parameters
  // Based on official API documentation and default values
  let apiSettings = {
    // Official Hunyuan3D-2.1 defaults with 12GB VRAM optimization
    guidance_scale: 5.0,  // Official default for 2.1
    num_inference_steps: 5, // Official default for 2.1 (much faster than 2.0)
    octree_resolution: 256,  // Standard resolution

    // Texture generation enabled by default
    texture: true, // Enable PBR texture generation
    remove_background: true, // Automatic background removal

    // Processing settings optimized for 12GB VRAM
    num_chunks: 8000, // Official default for 2.1

    // Seed handling
    seed: settings.seed || Math.floor(Math.random() * 1000000)
  };

  logHunyaunServer('Using official Hunyuan3D-2.1 settings with 12GB VRAM optimization');

  // Apply user overrides if provided (but keep within safe limits for 12GB VRAM)
  if (settings.guidance_scale !== undefined) {
    apiSettings.guidance_scale = Math.max(0.1, Math.min(20.0, settings.guidance_scale));
  }
  if (settings.num_inference_steps !== undefined) {
    apiSettings.num_inference_steps = Math.max(1, Math.min(20, settings.num_inference_steps));
  }
  if (settings.octree_resolution !== undefined) {
    apiSettings.octree_resolution = Math.max(64, Math.min(512, settings.octree_resolution));
  }

  logHunyaunServer('Mapped API settings:', apiSettings);

  // Prepare the request data for official Hunyuan3D-2.1 API
  const requestData = {
    image: imageBase64, // Base64 encoded image (no data URL prefix)
    remove_background: apiSettings.remove_background,
    texture: apiSettings.texture,
    seed: apiSettings.seed,
    octree_resolution: apiSettings.octree_resolution,
    num_inference_steps: apiSettings.num_inference_steps,
    guidance_scale: apiSettings.guidance_scale,
    num_chunks: apiSettings.num_chunks
  };

  logHunyaunServer('Final request data:', {
    ...requestData,
    image: '[IMAGE_DATA_OMITTED]' // Don't log the huge base64 image
  });

  try {
    // Send generation request to official Hunyuan3D-2.1 API (asynchronous)
    logHunyaunServer('Sending generation request to official Hunyuan3D-2.1 API...');
    const requestConfig = {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30 second timeout for task submission
    };

    let taskId = null;
    try {
      const response = await axios.post(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/send`, requestData, requestConfig);
      logHunyaunServer('Generation request sent successfully');

      if (response.data && response.data.uid) {
        taskId = response.data.uid;
        logHunyaunServer(`Generation started with task ID: ${taskId}`);
      } else {
        throw new Error('Invalid response from generation endpoint - no task ID received');
      }
    } catch (postError) {
      logHunyaunServer('POST request failed:', postError.code, postError.message);
      throw postError;
    }

    if (taskId) {
      // Poll for status updates using official API
      let statusComplete = false;
      let pollAttempts = 0;
      const maxPollAttempts = 5; // Give it a few attempts to start responding

      while (!statusComplete) {
        try {
          const statusResp = await axios.get(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/status/${taskId}`);
          const statusData = statusResp.data;
          pollAttempts = 0; // Reset poll attempts on successful response

          if (progressCb && statusData) {
            let progress = 0;
            let message = 'Processing...';

            // Map official API status to progress
            if (statusData.status === 'processing') {
              progress = 25;
              message = 'Generating 3D shape...';
            } else if (statusData.status === 'texturing') {
              progress = 75;
              message = 'Generating PBR textures...';
            } else if (statusData.status === 'completed') {
              progress = 100;
              message = 'Generation complete!';
              statusComplete = true;

              // Save the model data from the status response
              if (statusData.model_base64) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const outputPath = path.join(OUTPUT_DIR, `hunyuan3d_model_${timestamp}.glb`);

                // Ensure output directory exists
                if (!fs.existsSync(OUTPUT_DIR)) {
                  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
                }

                // Decode and save the model
                const modelBuffer = Buffer.from(statusData.model_base64, 'base64');
                fs.writeFileSync(outputPath, modelBuffer);

                logHunyaunServer('Model saved to:', outputPath);
                const app = require('electron').app;
                const relativePath = path.relative(app.getAppPath(), outputPath);

                // Return the path immediately since we have the model
                return relativePath;
              }
            } else if (statusData.status === 'error') {
              throw new Error(statusData.message || 'Generation failed');
            }

            progressCb({
              stage: 'hunyaun',
              progress: progress,
              step: progress,
              total: 100,
              message: message
            });
          }
        } catch (err) {
          pollAttempts++;
          logHunyaunServer(`Error polling /status/${taskId} endpoint (attempt ${pollAttempts}/${maxPollAttempts}):`, err.message);

          // If we can't poll status after several attempts, the server might be truly down
          if (pollAttempts >= maxPollAttempts) {
            throw new Error('Server not responding to status requests after POST hang up');
          }
        }

        if (!statusComplete) {
          await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds before next poll
        }
      }
    }

    // If we reach here without returning, something went wrong
    throw new Error('Generation completed but no model data received');

  } catch (error) {
    logHunyaunServer('Error during 3D generation:', error);

    // If we get connection errors, the server may have crashed or connection issues
    // Try to restart the server and retry once
    if (error.code === 'ECONNREFUSED' || error.code === 'ECONNRESET' ||
        error.message.includes('ECONNREFUSED') || error.message.includes('socket hang up')) {
      logHunyaunServer('Connection refused - server may have crashed. Attempting restart and retry...');

      try {
        // Force cleanup and restart
        await cleanupPythonProcesses();

        // Kill existing process if it exists
        if (global.hunyaunProcess) {
          logHunyaunServer('Killing existing Hunyaun process...');
          global.hunyaunProcess.kill('SIGTERM');
          global.hunyaunProcess = null;
        }

        // Wait for cleanup
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Restart server
        logHunyaunServer('Restarting server after connection failure...');
        startHunyaunServer(progressCb);
        await waitForHunyaunReady(null, progressCb);

        // Retry the generation request once
        logHunyaunServer('Retrying generation request after server restart...');
        return await retryGenerationRequest(imagePath, progressCb, settings);

      } catch (retryError) {
        logHunyaunServer('Retry after server restart failed:', retryError);
        throw retryError;
      }
    }

    throw error;
  }
}

module.exports = {
  generate3DModel,
  cleanupPythonProcesses,
  isHunyaunRunning,
  startHunyaunServer,
  waitForHunyaunReady
};
