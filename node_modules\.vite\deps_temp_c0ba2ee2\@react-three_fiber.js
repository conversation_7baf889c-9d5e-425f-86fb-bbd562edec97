import {
  Canvas,
  act,
  addAfterEffect,
  addEffect,
  addTail,
  advance,
  applyProps,
  buildGraph,
  context,
  createEvents,
  createPointerEvents,
  createPortal,
  createRoot,
  dispose,
  extend,
  flushGlobalEffects,
  flushSync,
  getRootState,
  invalidate,
  reconciler,
  render,
  roots,
  threeTypes,
  unmountComponentAtNode,
  useFrame,
  useGraph,
  useInstanceHandle,
  useLoader,
  useStore,
  useThree
} from "./chunk-OUDQPUDS.js";
import "./chunk-DKVX6RWU.js";
import "./chunk-6PXSGDAH.js";
import "./chunk-DRWLMN53.js";
import "./chunk-G3PMV62Z.js";
export {
  Canvas,
  threeTypes as ReactThreeFiber,
  roots as _roots,
  act,
  addAfterEffect,
  addEffect,
  addTail,
  advance,
  applyProps,
  buildGraph,
  context,
  createEvents,
  createPointerEvents,
  createPortal,
  createRoot,
  dispose,
  createPointerEvents as events,
  extend,
  flushGlobalEffects,
  flushSync,
  getRootState,
  invalidate,
  reconciler,
  render,
  unmountComponentAtNode,
  useFrame,
  useGraph,
  useInstanceHandle,
  useLoader,
  useStore,
  useThree
};
//# sourceMappingURL=@react-three_fiber.js.map
