@echo off
REM Hunyuan3D-2.1 Server Launcher
REM This script starts the API server (assumes installation is already complete)

echo Hunyuan3D-2.1 Server Launcher
echo ==============================

REM Set the current directory to the script location
cd /d "%~dp0"

REM Check if installation is complete
if not exist "hunyuan21_init_done.txt" (
    echo Error: Hunyuan3D-2.1 not installed
    echo Please run the dependency manager to install first
    exit /b 1
)

REM Activate virtual environment
if not exist "env\Scripts\activate.bat" (
    echo Error: Virtual environment not found
    echo Please run the dependency manager to install first
    exit /b 1
)

call env\Scripts\activate.bat
if errorlevel 1 (
    echo Failed to activate virtual environment
    exit /b 1
)

REM Set Python executable path (absolute path since we'll change directories)
set PYTHON_EXE=%~dp0env\Scripts\python.exe

REM Set environment variables for 12GB VRAM optimization
set CUDA_VISIBLE_DEVICES=0
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

REM Start the official Hunyuan3D-2.1 API Server
echo.
echo Starting Official Hunyuan3D-2.1 API Server...
echo Server will be available at: http://127.0.0.1:8080
echo Interactive API docs: http://127.0.0.1:8080/docs
echo Press Ctrl+C to stop the server
echo ================================================

REM Change to the official Hunyuan3D-2.1 directory
if not exist "Hunyuan3D-2.1-main" (
    echo Error: Hunyuan3D-2.1-main directory not found
    echo Please ensure the official repository is cloned
    exit /b 1
)

cd Hunyuan3D-2.1-main

REM Verify the API server exists
if not exist "api_server.py" (
    echo Error: api_server.py not found in Hunyuan3D-2.1-main directory
    echo Please ensure the official repository is complete
    exit /b 1
)

REM Start the official API server with proper parameters
echo Starting Official Hunyuan3D-2.1 API Server on 127.0.0.1:8080
echo Python executable: %PYTHON_EXE%
echo Working directory: %CD%
echo.
"%PYTHON_EXE%" api_server.py ^
    --host 127.0.0.1 ^
    --port 8080 ^
    --model_path tencent/Hunyuan3D-2.1 ^
    --subfolder hunyuan3d-dit-v2-1 ^
    --device cuda ^
    --cache-path ../outputs ^
    --low_vram_mode
