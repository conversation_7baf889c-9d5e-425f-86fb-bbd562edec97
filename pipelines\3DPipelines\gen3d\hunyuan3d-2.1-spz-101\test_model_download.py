#!/usr/bin/env python3
"""
Test script to verify Hunyuan3D-2.1 model downloads work correctly
"""

import sys
import os
from pathlib import Path

# Add paths
current_dir = Path(__file__).parent
hunyuan_dir = current_dir / "Hunyuan3D-2.1-main"
sys.path.insert(0, str(hunyuan_dir))
sys.path.insert(0, str(hunyuan_dir / "hy3dshape"))
sys.path.insert(0, str(hunyuan_dir / "hy3dpaint"))

def test_model_downloads():
    """Test that Hunyuan3D-2.1 models can be downloaded and loaded"""
    
    print("Testing Hunyuan3D-2.1 model downloads...")
    print("=" * 50)
    
    try:
        # Test basic imports
        print("1. Testing imports...")
        from hy3dshape.pipelines import Hunyuan3DDiTFlowMatchingPipeline
        from textureGenPipeline import Hunyuan3DPaintPipeline, Hunyuan3DPaintConfig
        print("   [OK] Imports successful")
        
        # Test shape generation model loading
        print("2. Testing shape generation model...")
        model_path = 'tencent/Hunyuan3D-2.1'
        subfolder = 'hunyuan3d-dit-v2-1'
        
        print(f"   Loading from: {model_path}/{subfolder}")
        shape_pipeline = Hunyuan3DDiTFlowMatchingPipeline.from_pretrained(
            model_path, 
            subfolder=subfolder
        )
        print("   [OK] Shape generation model loaded successfully")

        # Clean up to save memory
        del shape_pipeline

        # Test texture generation model loading
        print("3. Testing texture generation model...")
        try:
            paint_config = Hunyuan3DPaintConfig(max_num_view=6, resolution=512)
            paint_pipeline = Hunyuan3DPaintPipeline(paint_config)
            print("   [OK] Texture generation model loaded successfully")
            del paint_pipeline
        except Exception as e:
            print(f"   [WARNING] Texture generation model failed: {e}")
            print("   This is expected if C++ extensions are not built")

        print("=" * 50)
        print("[OK] Model download test completed successfully!")
        print("Models are cached and ready for use.")
        return True

    except Exception as e:
        print(f"[ERROR] Model download test failed: {e}")
        print("This may be due to:")
        print("- Network connectivity issues")
        print("- Missing dependencies")
        print("- Insufficient disk space")
        print("- Authentication issues with Hugging Face")
        return False

if __name__ == "__main__":
    success = test_model_downloads()
    sys.exit(0 if success else 1)
