@echo off
REM Official Hunyuan3D-2.1 Installation and Server Launcher
REM This script installs and runs the official API server with 12GB VRAM optimizations

echo Official Hunyuan3D-2.1 Setup and Server
echo ================================================

REM Set the current directory to the script location
cd /d "%~dp0"

REM Use the embedded Python with development headers
set PYTHON_EXE=%~dp0python\python.exe
if not exist "%PYTHON_EXE%" (
    echo Error: Embedded Python not found at %PYTHON_EXE%
    echo Please ensure the embedded Python is properly installed.
    pause
    exit /b 1
)

REM Check if virtual environment exists, create if not
if not exist "env\Scripts\activate.bat" (
    echo Creating virtual environment with embedded Python...
    "%PYTHON_EXE%" -m venv env
    if errorlevel 1 (
        echo Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
call env\Scripts\activate.bat
if errorlevel 1 (
    echo Failed to activate virtual environment
    pause
    exit /b 1
)

REM Check if Hunyuan3D-2.1 repository exists, clone if not
if not exist "Hunyuan3D-2.1-main" (
    echo Cloning official Hunyuan3D-2.1 repository...
    git clone https://github.com/Tencent-Hunyuan/Hunyuan3D-2.1.git Hunyuan3D-2.1-main
    if errorlevel 1 (
        echo Failed to clone repository
        pause
        exit /b 1
    )
)

REM Install build tools and dependencies first
echo Installing build tools and core dependencies...
pip install --upgrade pip setuptools wheel
pip install ninja pybind11

REM Install PyTorch with CUDA support
echo Installing PyTorch with CUDA support...
pip install torch==2.5.1 torchvision==0.20.1 torchaudio==2.5.1 --index-url https://download.pytorch.org/whl/cu124

REM Install onnxruntime explicitly (fixes the onnxruntime.__spec__ issue)
echo Installing onnxruntime...
pip install onnxruntime-gpu

REM Install requirements from official repository
if exist "Hunyuan3D-2.1-main\requirements.txt" (
    echo Installing requirements from official repository...
    pip install -r Hunyuan3D-2.1-main\requirements.txt
)

REM Install the package in development mode
echo Installing Hunyuan3D-2.1 in development mode...
cd Hunyuan3D-2.1-main
pip install -e .

REM Build custom rasterizer if it exists
if exist "hy3dpaint\custom_rasterizer" (
    echo Building custom rasterizer...
    cd hy3dpaint\custom_rasterizer
    pip install -e .
    if errorlevel 1 (
        echo Warning: Failed to build custom rasterizer - continuing without it
        echo This may affect texture generation quality but basic functionality should work
    )
    cd ..\..
)

REM Build mesh painter if it exists
if exist "hy3dpaint\DifferentiableRenderer" (
    echo Building mesh painter...
    cd hy3dpaint\DifferentiableRenderer
    python setup_mesh_inpaint.py build_ext --inplace
    if errorlevel 1 (
        echo Warning: Failed to build mesh painter - continuing without it
        echo This may affect texture generation quality but basic functionality should work
    )
    cd ..\..
)

REM Download Real-ESRGAN model if needed
if not exist "hy3dpaint\ckpt\RealESRGAN_x4plus.pth" (
    echo Downloading Real-ESRGAN model...
    mkdir hy3dpaint\ckpt 2>nul
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth' -OutFile 'hy3dpaint\ckpt\RealESRGAN_x4plus.pth'"
)

REM Set environment variables for 12GB VRAM optimization
set CUDA_VISIBLE_DEVICES=0
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

REM Run the official API server with low VRAM mode
echo.
echo Starting Hunyuan3D-2.1 API Server on port 8080...
echo Server will be available at: http://127.0.0.1:8080
echo.
echo Press Ctrl+C to stop the server
echo ================================================

python api_server.py --host 127.0.0.1 --port 8080 --low_vram_mode

pause
