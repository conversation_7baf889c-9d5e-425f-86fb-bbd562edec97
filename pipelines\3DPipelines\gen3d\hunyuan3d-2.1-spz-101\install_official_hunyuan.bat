@echo off
REM Official Hu<PERSON>uan3D-2.1 Installation Script
REM This script properly installs the official Hunyuan3D-2.1 with C++ extensions

echo Official Hunyuan3D-2.1 Installation
echo ====================================

REM Set the current directory to the script location
cd /d "%~dp0"

REM Use virtual environment Python instead of system Python
if exist "env\Scripts\python.exe" (
    echo Using virtual environment Python: env\Scripts\python.exe
    set PYTHON_EXE=env\Scripts\python.exe
) else (
    echo Error: Virtual environment not found
    echo Please run the dependency manager to create virtual environment first
    pause
    exit /b 1
)

echo Virtual environment Python found and ready

REM Use existing virtual environment (created by dependency manager)
if not exist "env" (
    echo Error: Virtual environment not found
    echo The dependency manager should have created this already
    pause
    exit /b 1
)

echo Using existing virtual environment...

REM Activate virtual environment
call env\Scripts\activate.bat
if errorlevel 1 (
    echo Failed to activate virtual environment
    pause
    exit /b 1
)

REM Upgrade pip and install build tools
echo Installing build tools...
%PYTHON_EXE% -m pip install --upgrade pip setuptools wheel
%PYTHON_EXE% -m pip install ninja pybind11

REM Set up Visual Studio build environment if available
call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if not errorlevel 1 (
    echo Visual Studio Build Tools found and configured
) else (
    echo Warning: Visual Studio Build Tools not found
    echo C++ extensions may fail to build
)

REM Install PyTorch with CUDA support
echo Installing PyTorch with CUDA support...
%PYTHON_EXE% -m pip install torch==2.5.1 torchvision==0.20.1 torchaudio==2.5.1 --index-url https://download.pytorch.org/whl/cu124

REM Clone official repository if not exists
if not exist "Hunyuan3D-2.1-main" (
    echo Cloning official Hunyuan3D-2.1 repository...
    git clone https://github.com/Tencent-Hunyuan/Hunyuan3D-2.1.git Hunyuan3D-2.1-main
    if errorlevel 1 (
        echo Failed to clone repository
        pause
        exit /b 1
    )
)

REM Install requirements from official repository
if exist "Hunyuan3D-2.1-main\requirements.txt" (
    echo Installing requirements from official repository...
    %PYTHON_EXE% -m pip install -r Hunyuan3D-2.1-main\requirements.txt
)

REM Install additional dependencies for API server and Hunyuan3D-2.1
echo Installing additional dependencies...
%PYTHON_EXE% -m pip install fastapi uvicorn pydantic
%PYTHON_EXE% -m pip install rembg trimesh
%PYTHON_EXE% -m pip install huggingface-hub safetensors

REM Change to repository directory
cd Hunyuan3D-2.1-main

REM Skip main package installation (no setup.py in root)
echo Skipping main package installation (no setup.py in root directory)

REM Build custom rasterizer with Windows compatibility fixes
if exist "hy3dpaint\custom_rasterizer" (
    echo Building custom rasterizer...
    cd hy3dpaint\custom_rasterizer

    REM Set CUDA environment variables
    set FORCE_CUDA=1
    set TORCH_CUDA_ARCH_LIST=7.5;8.0;8.6;8.9;9.0

    echo Attempting custom rasterizer build...
    %PYTHON_EXE% -m pip install -e . --no-build-isolation --verbose
    if errorlevel 1 (
        echo Warning: Custom rasterizer build failed
        echo Texture generation may have reduced quality
    ) else (
        echo Custom rasterizer built successfully
    )
    cd ..\..
) else (
    echo Warning: custom_rasterizer directory not found
    echo Texture generation may have reduced quality
)

REM Build DifferentiableRenderer with Windows compatibility fixes
if exist "hy3dpaint\DifferentiableRenderer" (
    echo Building DifferentiableRenderer...
    cd hy3dpaint\DifferentiableRenderer

    REM Set environment variables
    set TORCH_CUDA_ARCH_LIST=7.5;8.0;8.6;8.9;9.0
    set FORCE_CUDA=1

    REM Try Python setup if available
    if exist "setup_mesh_inpaint.py" (
        echo Attempting DifferentiableRenderer build...
        %PYTHON_EXE% setup_mesh_inpaint.py build_ext --inplace --verbose
        if errorlevel 1 (
            echo Warning: DifferentiableRenderer build failed
            echo Advanced texture features may not work optimally
        ) else (
            echo DifferentiableRenderer built successfully
        )
    ) else (
        echo Warning: No setup script found for DifferentiableRenderer
        echo Advanced texture features may not work optimally
    )
    cd ..\..
) else (
    echo Warning: DifferentiableRenderer directory not found
    echo Advanced texture features may not work optimally
)

REM Download Real-ESRGAN model (with timeout)
if not exist "hy3dpaint\ckpt\RealESRGAN_x4plus.pth" (
    echo Downloading Real-ESRGAN model...
    mkdir hy3dpaint\ckpt 2>nul
    timeout 120 powershell -Command "try { Invoke-WebRequest -Uri 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth' -OutFile 'hy3dpaint\ckpt\RealESRGAN_x4plus.pth' -TimeoutSec 120 } catch { Write-Host 'Failed to download Real-ESRGAN model' }"
    if errorlevel 1 (
        echo Warning: Real-ESRGAN model download failed or timed out
        echo Texture upscaling may not work optimally
    )
)

REM Return to main directory
cd ..

REM Test model access and trigger automatic download
echo Testing Hunyuan3D-2.1 model access...
echo This will trigger automatic model download and caching
%PYTHON_EXE% test_model_download.py
if errorlevel 1 (
    echo Warning: Model download test failed
    echo Models will be downloaded automatically on first generation
) else (
    echo Models downloaded and cached successfully
)

REM Create fallback stub modules if C++ extensions failed
echo Creating fallback stub modules for failed C++ extensions...

REM Create stub for custom_rasterizer if it failed
if not exist "hy3dpaint\custom_rasterizer\custom_rasterizer.pyd" (
    echo Creating stub for custom_rasterizer...
    echo # Stub module for custom_rasterizer > hy3dpaint\custom_rasterizer\__init__.py
    echo print("Warning: Using stub custom_rasterizer - C++ extension failed to build") >> hy3dpaint\custom_rasterizer\__init__.py
    echo. >> hy3dpaint\custom_rasterizer\__init__.py
    echo def rasterize_gaussians(*args, **kwargs): >> hy3dpaint\custom_rasterizer\__init__.py
    echo     raise NotImplementedError("Custom rasterizer C++ extension not available") >> hy3dpaint\custom_rasterizer\__init__.py
)

REM Create stub for mesh_inpaint_processor if it failed
if not exist "hy3dpaint\DifferentiableRenderer\mesh_inpaint_processor.pyd" (
    echo Creating stub for mesh_inpaint_processor...
    echo # Stub module for mesh_inpaint_processor > hy3dpaint\DifferentiableRenderer\mesh_inpaint_processor.py
    echo print("Warning: Using stub mesh_inpaint_processor - C++ extension failed to build") >> hy3dpaint\DifferentiableRenderer\mesh_inpaint_processor.py
    echo. >> hy3dpaint\DifferentiableRenderer\mesh_inpaint_processor.py
    echo def process_mesh(*args, **kwargs): >> hy3dpaint\DifferentiableRenderer\mesh_inpaint_processor.py
    echo     raise NotImplementedError("Mesh inpaint processor C++ extension not available") >> hy3dpaint\DifferentiableRenderer\mesh_inpaint_processor.py
)

REM Create installation completion marker
echo Installation completed successfully > hunyuan21_init_done.txt
echo Installation date: %date% %time% >> hunyuan21_init_done.txt
echo C++ extensions status: >> hunyuan21_init_done.txt
if exist "hy3dpaint\custom_rasterizer\custom_rasterizer.pyd" (
    echo   Custom rasterizer: Built successfully >> hunyuan21_init_done.txt
) else (
    echo   Custom rasterizer: Using stub fallback >> hunyuan21_init_done.txt
)
if exist "hy3dpaint\DifferentiableRenderer\mesh_inpaint_processor.pyd" (
    echo   Mesh inpaint processor: Built successfully >> hunyuan21_init_done.txt
) else (
    echo   Mesh inpaint processor: Using stub fallback >> hunyuan21_init_done.txt
)

echo.
echo ====================================
echo Installation completed!
echo ====================================
echo.
echo The official Hunyuan3D-2.1 has been installed with:
echo - Official repository cloned
echo - Python dependencies installed
echo - C++ extensions built (if successful)
echo - Real-ESRGAN model downloaded
echo.
echo You can now use Hunyuan3D-2.1 for high-quality 3D generation.
echo.
pause
